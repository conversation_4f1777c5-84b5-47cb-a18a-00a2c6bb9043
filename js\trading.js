// Trading Manager - Handles order placement, modification, and automated trading logic
class TradingManager {
    constructor() {
        this.activeOrders = new Map();
        this.positions = new Map();
        this.stopLossOrders = new Map();
        this.targetOrders = new Map();
        this.automatedTrades = new Map();
        this.dailyStats = {
            ordersPlaced: 0,
            ordersExecuted: 0,
            totalPnL: 0,
            maxDrawdown: 0
        };
        
        // Risk management
        this.riskManager = new RiskManager();
        
        // Initialize event listeners
        this.initializeEventListeners();
        
        // Start monitoring loop
        this.startMonitoring();
    }
    
    initializeEventListeners() {
        // Listen for WebSocket order updates
        wsManager.addMessageHandler('order', (data) => {
            this.handleOrderUpdate(data);
        });
        
        // Listen for market data updates for stop loss/target monitoring
        wsManager.addMessageHandler('quote', (data) => {
            this.monitorStopLossAndTargets(data);
        });
    }
    
    // Order Placement Methods
    async placeOrder(orderParams) {
        try {
            // Validate order parameters
            const validation = this.validateOrder(orderParams);
            if (!validation.isValid) {
                throw new Error(validation.error);
            }
            
            // Check risk management
            const riskCheck = await this.riskManager.checkOrder(orderParams);
            if (!riskCheck.allowed) {
                throw new Error(riskCheck.reason);
            }
            
            // Prepare order data for API
            const orderData = this.prepareOrderData(orderParams);
            
            // Place order via API
            const result = await kotakAPI.placeOrder(orderData);
            
            if (result.success) {
                // Store order details
                const orderId = result.data.orderId || result.data.norenordno;
                this.activeOrders.set(orderId, {
                    ...orderParams,
                    orderId,
                    status: 'PENDING',
                    timestamp: new Date(),
                    apiResponse: result.data
                });
                
                // Set up stop loss and target if specified
                if (orderParams.stopLoss || orderParams.target) {
                    this.setupStopLossAndTarget(orderId, orderParams);
                }
                
                // Update daily stats
                this.dailyStats.ordersPlaced++;
                
                Logger.log('Order placed successfully', result.data);
                this.notifyOrderPlaced(orderId, orderParams);
                
                return { success: true, orderId, data: result.data };
            } else {
                throw new Error(result.error);
            }
            
        } catch (error) {
            Logger.error('Failed to place order', error);
            this.notifyOrderError(orderParams, error.message);
            return { success: false, error: error.message };
        }
    }
    
    async modifyOrder(orderId, modifications) {
        try {
            const order = this.activeOrders.get(orderId);
            if (!order) {
                throw new Error('Order not found');
            }
            
            const result = await kotakAPI.modifyOrder(orderId, modifications);
            
            if (result.success) {
                // Update stored order
                Object.assign(order, modifications);
                order.lastModified = new Date();
                
                Logger.log('Order modified successfully', result.data);
                this.notifyOrderModified(orderId, modifications);
                
                return { success: true, data: result.data };
            } else {
                throw new Error(result.error);
            }
            
        } catch (error) {
            Logger.error('Failed to modify order', error);
            return { success: false, error: error.message };
        }
    }
    
    async cancelOrder(orderId) {
        try {
            const order = this.activeOrders.get(orderId);
            if (!order) {
                throw new Error('Order not found');
            }
            
            const result = await kotakAPI.cancelOrder(orderId);
            
            if (result.success) {
                // Update order status
                order.status = 'CANCELLED';
                order.cancelledAt = new Date();
                
                // Remove from active orders
                this.activeOrders.delete(orderId);
                
                // Cancel associated stop loss and target orders
                this.cancelStopLossAndTarget(orderId);
                
                Logger.log('Order cancelled successfully', result.data);
                this.notifyOrderCancelled(orderId);
                
                return { success: true, data: result.data };
            } else {
                throw new Error(result.error);
            }
            
        } catch (error) {
            Logger.error('Failed to cancel order', error);
            return { success: false, error: error.message };
        }
    }
    
    // Order Validation
    validateOrder(orderParams) {
        const required = ['symbol', 'exchange', 'transactionType', 'quantity'];
        
        for (const field of required) {
            if (!orderParams[field]) {
                return { isValid: false, error: `Missing required field: ${field}` };
            }
        }
        
        // Validate quantity
        if (orderParams.quantity <= 0) {
            return { isValid: false, error: 'Quantity must be greater than 0' };
        }
        
        // Validate price for limit orders
        if (orderParams.orderType === 'LMT' && (!orderParams.price || orderParams.price <= 0)) {
            return { isValid: false, error: 'Price is required for limit orders' };
        }
        
        // Validate stop loss price
        if (orderParams.orderType === 'SL' && (!orderParams.triggerPrice || orderParams.triggerPrice <= 0)) {
            return { isValid: false, error: 'Trigger price is required for stop loss orders' };
        }
        
        return { isValid: true };
    }
    
    // Prepare order data for API
    prepareOrderData(orderParams) {
        return {
            tradingsymbol: orderParams.symbol,
            exchange: orderParams.exchange,
            transactiontype: orderParams.transactionType,
            ordertype: orderParams.orderType || 'MKT',
            product: orderParams.product || CONFIG.TRADING.DEFAULT_PRODUCT,
            quantity: orderParams.quantity.toString(),
            price: orderParams.price ? orderParams.price.toString() : '0',
            triggerprice: orderParams.triggerPrice ? orderParams.triggerPrice.toString() : '0',
            validity: orderParams.validity || 'DAY',
            disclosed_quantity: orderParams.disclosedQuantity || '0',
            amo: orderParams.amo || 'NO'
        };
    }
    
    // Stop Loss and Target Management
    setupStopLossAndTarget(orderId, orderParams) {
        if (orderParams.stopLoss) {
            this.stopLossOrders.set(orderId, {
                orderId,
                symbol: orderParams.symbol,
                exchange: orderParams.exchange,
                quantity: orderParams.quantity,
                stopLossPrice: orderParams.stopLoss,
                transactionType: orderParams.transactionType === 'B' ? 'S' : 'B', // Opposite transaction
                isActive: false // Will be activated when main order is filled
            });
        }
        
        if (orderParams.target) {
            this.targetOrders.set(orderId, {
                orderId,
                symbol: orderParams.symbol,
                exchange: orderParams.exchange,
                quantity: orderParams.quantity,
                targetPrice: orderParams.target,
                transactionType: orderParams.transactionType === 'B' ? 'S' : 'B', // Opposite transaction
                isActive: false // Will be activated when main order is filled
            });
        }
    }
    
    activateStopLossAndTarget(orderId) {
        if (this.stopLossOrders.has(orderId)) {
            const stopLoss = this.stopLossOrders.get(orderId);
            stopLoss.isActive = true;
            Logger.log(`Stop loss activated for order ${orderId}`);
        }
        
        if (this.targetOrders.has(orderId)) {
            const target = this.targetOrders.get(orderId);
            target.isActive = true;
            Logger.log(`Target activated for order ${orderId}`);
        }
    }
    
    cancelStopLossAndTarget(orderId) {
        this.stopLossOrders.delete(orderId);
        this.targetOrders.delete(orderId);
        Logger.log(`Stop loss and target cancelled for order ${orderId}`);
    }
    
    // Monitor stop loss and target prices
    monitorStopLossAndTargets(marketData) {
        const symbol = marketData.symbol;
        const exchange = marketData.exchange;
        const currentPrice = parseFloat(marketData.ltp || marketData.price);
        
        if (!currentPrice) return;
        
        // Check stop loss orders
        for (const [orderId, stopLoss] of this.stopLossOrders) {
            if (stopLoss.isActive && 
                stopLoss.symbol === symbol && 
                stopLoss.exchange === exchange) {
                
                const shouldTrigger = this.shouldTriggerStopLoss(stopLoss, currentPrice);
                if (shouldTrigger) {
                    this.executeStopLoss(orderId, stopLoss, currentPrice);
                }
            }
        }
        
        // Check target orders
        for (const [orderId, target] of this.targetOrders) {
            if (target.isActive && 
                target.symbol === symbol && 
                target.exchange === exchange) {
                
                const shouldTrigger = this.shouldTriggerTarget(target, currentPrice);
                if (shouldTrigger) {
                    this.executeTarget(orderId, target, currentPrice);
                }
            }
        }
    }
    
    shouldTriggerStopLoss(stopLoss, currentPrice) {
        if (stopLoss.transactionType === 'S') {
            // For sell orders (original was buy), trigger if price falls below stop loss
            return currentPrice <= stopLoss.stopLossPrice;
        } else {
            // For buy orders (original was sell), trigger if price rises above stop loss
            return currentPrice >= stopLoss.stopLossPrice;
        }
    }
    
    shouldTriggerTarget(target, currentPrice) {
        if (target.transactionType === 'S') {
            // For sell orders (original was buy), trigger if price rises above target
            return currentPrice >= target.targetPrice;
        } else {
            // For buy orders (original was sell), trigger if price falls below target
            return currentPrice <= target.targetPrice;
        }
    }
    
    async executeStopLoss(orderId, stopLoss, currentPrice) {
        try {
            Logger.log(`Executing stop loss for order ${orderId} at price ${currentPrice}`);
            
            const orderParams = {
                symbol: stopLoss.symbol,
                exchange: stopLoss.exchange,
                transactionType: stopLoss.transactionType,
                quantity: stopLoss.quantity,
                orderType: 'MKT',
                product: CONFIG.TRADING.DEFAULT_PRODUCT
            };
            
            const result = await this.placeOrder(orderParams);
            
            if (result.success) {
                // Remove the stop loss order
                this.stopLossOrders.delete(orderId);
                
                // Also remove target if exists
                this.targetOrders.delete(orderId);
                
                this.notifyStopLossExecuted(orderId, currentPrice);
                Logger.log(`Stop loss executed successfully for order ${orderId}`);
            }
            
        } catch (error) {
            Logger.error(`Failed to execute stop loss for order ${orderId}`, error);
        }
    }
    
    async executeTarget(orderId, target, currentPrice) {
        try {
            Logger.log(`Executing target for order ${orderId} at price ${currentPrice}`);
            
            const orderParams = {
                symbol: target.symbol,
                exchange: target.exchange,
                transactionType: target.transactionType,
                quantity: target.quantity,
                orderType: 'MKT',
                product: CONFIG.TRADING.DEFAULT_PRODUCT
            };
            
            const result = await this.placeOrder(orderParams);
            
            if (result.success) {
                // Remove the target order
                this.targetOrders.delete(orderId);
                
                // Also remove stop loss if exists
                this.stopLossOrders.delete(orderId);
                
                this.notifyTargetExecuted(orderId, currentPrice);
                Logger.log(`Target executed successfully for order ${orderId}`);
            }
            
        } catch (error) {
            Logger.error(`Failed to execute target for order ${orderId}`, error);
        }
    }
    
    // Order Update Handler
    handleOrderUpdate(orderData) {
        const orderId = orderData.orderId || orderData.norenordno;
        
        if (this.activeOrders.has(orderId)) {
            const order = this.activeOrders.get(orderId);
            
            // Update order status
            order.status = orderData.status;
            order.lastUpdate = new Date();
            
            // Handle different order statuses
            switch (orderData.status) {
                case 'COMPLETE':
                case 'FILLED':
                    this.handleOrderFilled(orderId, orderData);
                    break;
                case 'CANCELLED':
                    this.handleOrderCancelled(orderId, orderData);
                    break;
                case 'REJECTED':
                    this.handleOrderRejected(orderId, orderData);
                    break;
            }
            
            // Notify UI
            this.notifyOrderUpdate(orderId, orderData);
        }
    }
    
    handleOrderFilled(orderId, orderData) {
        const order = this.activeOrders.get(orderId);
        
        // Update daily stats
        this.dailyStats.ordersExecuted++;
        
        // Activate stop loss and target
        this.activateStopLossAndTarget(orderId);
        
        // Update positions
        this.updatePosition(order, orderData);
        
        // Remove from active orders
        this.activeOrders.delete(orderId);
        
        Logger.log(`Order ${orderId} filled`);
        this.notifyOrderFilled(orderId, orderData);
    }
    
    handleOrderCancelled(orderId, orderData) {
        // Remove from active orders
        this.activeOrders.delete(orderId);
        
        // Cancel stop loss and target
        this.cancelStopLossAndTarget(orderId);
        
        Logger.log(`Order ${orderId} cancelled`);
    }
    
    handleOrderRejected(orderId, orderData) {
        // Remove from active orders
        this.activeOrders.delete(orderId);
        
        // Cancel stop loss and target
        this.cancelStopLossAndTarget(orderId);
        
        Logger.log(`Order ${orderId} rejected: ${orderData.reason}`);
        this.notifyOrderRejected(orderId, orderData);
    }
    
    // Position Management
    updatePosition(order, orderData) {
        const positionKey = `${order.symbol}_${order.exchange}`;
        
        if (!this.positions.has(positionKey)) {
            this.positions.set(positionKey, {
                symbol: order.symbol,
                exchange: order.exchange,
                quantity: 0,
                averagePrice: 0,
                totalValue: 0,
                pnl: 0,
                unrealizedPnl: 0
            });
        }
        
        const position = this.positions.get(positionKey);
        const fillQuantity = parseInt(orderData.fillshares || orderData.quantity);
        const fillPrice = parseFloat(orderData.avgprc || orderData.price);
        
        if (order.transactionType === 'B') {
            // Buy order - increase position
            const newTotalValue = position.totalValue + (fillQuantity * fillPrice);
            const newQuantity = position.quantity + fillQuantity;
            position.averagePrice = newTotalValue / newQuantity;
            position.quantity = newQuantity;
            position.totalValue = newTotalValue;
        } else {
            // Sell order - decrease position
            const soldValue = fillQuantity * fillPrice;
            const costValue = fillQuantity * position.averagePrice;
            const realizedPnl = soldValue - costValue;
            
            position.quantity -= fillQuantity;
            position.totalValue -= costValue;
            position.pnl += realizedPnl;
            
            // Update daily P&L
            this.dailyStats.totalPnL += realizedPnl;
        }
        
        Logger.log(`Position updated for ${positionKey}`, position);
    }
    
    // Monitoring and Cleanup
    startMonitoring() {
        // Monitor orders and positions every second
        setInterval(() => {
            this.monitorActiveOrders();
            this.updateUnrealizedPnL();
        }, 1000);
        
        // Refresh data from API every 30 seconds
        setInterval(() => {
            this.refreshOrdersFromAPI();
            this.refreshPositionsFromAPI();
        }, 30000);
    }
    
    monitorActiveOrders() {
        // Check for stale orders and other monitoring tasks
        const now = new Date();
        
        for (const [orderId, order] of this.activeOrders) {
            const timeDiff = now - order.timestamp;
            
            // Log warning for orders pending too long
            if (timeDiff > 300000) { // 5 minutes
                Logger.warn(`Order ${orderId} has been pending for ${Math.round(timeDiff / 60000)} minutes`);
            }
        }
    }
    
    async refreshOrdersFromAPI() {
        try {
            const result = await kotakAPI.getOrderBook();
            if (result.success) {
                // Update active orders with latest data
                // Implementation depends on API response format
                Logger.log('Orders refreshed from API');
            }
        } catch (error) {
            Logger.error('Failed to refresh orders from API', error);
        }
    }
    
    async refreshPositionsFromAPI() {
        try {
            const result = await kotakAPI.getPositions();
            if (result.success) {
                // Update positions with latest data
                // Implementation depends on API response format
                Logger.log('Positions refreshed from API');
            }
        } catch (error) {
            Logger.error('Failed to refresh positions from API', error);
        }
    }
    
    updateUnrealizedPnL() {
        // Update unrealized P&L for open positions
        // This would require current market prices
        // Implementation depends on market data availability
    }
    
    // Notification Methods
    notifyOrderPlaced(orderId, orderParams) {
        const event = new CustomEvent('orderPlaced', {
            detail: { orderId, orderParams }
        });
        document.dispatchEvent(event);
    }
    
    notifyOrderFilled(orderId, orderData) {
        const event = new CustomEvent('orderFilled', {
            detail: { orderId, orderData }
        });
        document.dispatchEvent(event);
        
        // Play sound notification if enabled
        if (CONFIG.NOTIFICATIONS.ENABLE_SOUND_ALERTS) {
            this.playSound('ORDER_FILLED');
        }
    }
    
    notifyOrderCancelled(orderId) {
        const event = new CustomEvent('orderCancelled', {
            detail: { orderId }
        });
        document.dispatchEvent(event);
    }
    
    notifyOrderRejected(orderId, orderData) {
        const event = new CustomEvent('orderRejected', {
            detail: { orderId, orderData }
        });
        document.dispatchEvent(event);
        
        // Play error sound
        if (CONFIG.NOTIFICATIONS.ENABLE_SOUND_ALERTS) {
            this.playSound('ERROR');
        }
    }
    
    notifyOrderModified(orderId, modifications) {
        const event = new CustomEvent('orderModified', {
            detail: { orderId, modifications }
        });
        document.dispatchEvent(event);
    }
    
    notifyOrderError(orderParams, error) {
        const event = new CustomEvent('orderError', {
            detail: { orderParams, error }
        });
        document.dispatchEvent(event);
        
        // Play error sound
        if (CONFIG.NOTIFICATIONS.ENABLE_SOUND_ALERTS) {
            this.playSound('ERROR');
        }
    }
    
    notifyOrderUpdate(orderId, orderData) {
        const event = new CustomEvent('orderUpdate', {
            detail: { orderId, orderData }
        });
        document.dispatchEvent(event);
    }
    
    notifyStopLossExecuted(orderId, price) {
        const event = new CustomEvent('stopLossExecuted', {
            detail: { orderId, price }
        });
        document.dispatchEvent(event);
        
        // Play stop loss sound
        if (CONFIG.NOTIFICATIONS.ENABLE_SOUND_ALERTS) {
            this.playSound('STOP_LOSS_HIT');
        }
    }
    
    notifyTargetExecuted(orderId, price) {
        const event = new CustomEvent('targetExecuted', {
            detail: { orderId, price }
        });
        document.dispatchEvent(event);
        
        // Play target sound
        if (CONFIG.NOTIFICATIONS.ENABLE_SOUND_ALERTS) {
            this.playSound('TARGET_REACHED');
        }
    }
    
    playSound(soundType) {
        try {
            const soundFile = CONFIG.NOTIFICATIONS.SOUND_FILES[soundType];
            if (soundFile) {
                const audio = new Audio(soundFile);
                audio.play().catch(error => {
                    Logger.warn('Failed to play sound', error);
                });
            }
        } catch (error) {
            Logger.warn('Error playing sound', error);
        }
    }
    
    // Getter Methods
    getActiveOrders() {
        return Array.from(this.activeOrders.values());
    }
    
    getPositions() {
        return Array.from(this.positions.values());
    }
    
    getDailyStats() {
        return { ...this.dailyStats };
    }
    
    getStopLossOrders() {
        return Array.from(this.stopLossOrders.values());
    }
    
    getTargetOrders() {
        return Array.from(this.targetOrders.values());
    }
}

// Risk Manager Class
class RiskManager {
    constructor() {
        this.dailyLoss = 0;
        this.dailyOrderCount = 0;
        this.orderCountPerMinute = 0;
        this.lastMinuteReset = Date.now();
    }
    
    async checkOrder(orderParams) {
        // Check daily loss limit
        if (this.dailyLoss >= CONFIG.RISK_MANAGEMENT.MAX_DAILY_LOSS) {
            return { allowed: false, reason: 'Daily loss limit exceeded' };
        }
        
        // Check order count limits
        this.updateOrderCounts();
        
        if (this.dailyOrderCount >= CONFIG.RISK_MANAGEMENT.MAX_ORDERS_PER_DAY) {
            return { allowed: false, reason: 'Daily order limit exceeded' };
        }
        
        if (this.orderCountPerMinute >= CONFIG.RISK_MANAGEMENT.MAX_ORDERS_PER_MINUTE) {
            return { allowed: false, reason: 'Order rate limit exceeded' };
        }
        
        // Check position size
        const orderValue = orderParams.quantity * (orderParams.price || 0);
        if (orderValue > CONFIG.RISK_MANAGEMENT.MAX_POSITION_SIZE) {
            return { allowed: false, reason: 'Position size limit exceeded' };
        }
        
        return { allowed: true };
    }
    
    updateOrderCounts() {
        const now = Date.now();
        
        // Reset per-minute counter if a minute has passed
        if (now - this.lastMinuteReset >= 60000) {
            this.orderCountPerMinute = 0;
            this.lastMinuteReset = now;
        }
        
        this.dailyOrderCount++;
        this.orderCountPerMinute++;
    }
    
    updateDailyLoss(loss) {
        this.dailyLoss += loss;
    }
    
    resetDailyCounters() {
        this.dailyLoss = 0;
        this.dailyOrderCount = 0;
        this.orderCountPerMinute = 0;
    }
}

// Create global trading manager instance
const tradingManager = new TradingManager();
