/* Trading Bot Styles */
:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --border-radius: 8px;
    --box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    margin: 0;
    padding: 0;
}

/* Header Styles */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-brand i {
    color: var(--success-color);
    margin-right: 10px;
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1rem;
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    font-weight: 600;
}

.card-header h5 {
    margin: 0;
    font-size: 1rem;
}

.card-header i {
    margin-right: 8px;
}

/* Button Styles */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #20c997);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #e74c3c);
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #f39c12);
    border: none;
    color: #333;
}

/* Form Styles */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #ddd;
    transition: border-color 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Market Data Styles */
.market-data-item {
    background: white;
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.market-data-item:hover {
    transform: translateX(5px);
    box-shadow: var(--box-shadow);
}

.symbol-name {
    font-weight: bold;
    font-size: 1.1rem;
    color: var(--dark-color);
}

.price-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
}

.current-price {
    font-size: 1.2rem;
    font-weight: bold;
}

.price-change {
    font-size: 0.9rem;
    padding: 2px 8px;
    border-radius: 12px;
    color: white;
}

.price-up {
    background-color: var(--success-color);
}

.price-down {
    background-color: var(--danger-color);
}

.price-neutral {
    background-color: #6c757d;
}

/* Order Styles */
.order-item {
    background: white;
    border-radius: var(--border-radius);
    padding: 12px;
    margin-bottom: 8px;
    border-left: 4px solid var(--info-color);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.order-symbol {
    font-weight: bold;
    color: var(--dark-color);
}

.order-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    color: white;
}

.status-pending {
    background-color: var(--warning-color);
    color: #333;
}

.status-filled {
    background-color: var(--success-color);
}

.status-cancelled {
    background-color: var(--danger-color);
}

.order-details {
    font-size: 0.9rem;
    color: #666;
}

/* Position Styles */
.position-item {
    background: white;
    border-radius: var(--border-radius);
    padding: 12px;
    margin-bottom: 8px;
    border-left: 4px solid var(--success-color);
}

.position-pnl {
    font-weight: bold;
}

.pnl-positive {
    color: var(--success-color);
}

.pnl-negative {
    color: var(--danger-color);
}

/* Scheduled Trade Styles */
.scheduled-trade-item {
    background: white;
    border-radius: var(--border-radius);
    padding: 12px;
    margin-bottom: 8px;
    border-left: 4px solid var(--warning-color);
}

.schedule-time {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 5px;
}

.schedule-action {
    font-weight: bold;
}

/* Connection Status */
.connection-status {
    display: flex;
    align-items: center;
}

.status-connected {
    color: var(--success-color) !important;
}

.status-disconnected {
    color: var(--danger-color) !important;
}

.status-connecting {
    color: var(--warning-color) !important;
}

/* Footer Styles */
footer {
    border-top: 1px solid #ddd;
    background: white !important;
}

footer small {
    font-weight: 500;
}

/* Animations */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 10px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .btn {
        font-size: 0.9rem;
    }
    
    .market-data-item,
    .order-item,
    .position-item,
    .scheduled-trade-item {
        padding: 10px;
    }
}

/* Loading Spinner */
.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Alert Styles */
.alert-custom {
    border-radius: var(--border-radius);
    border: none;
    font-weight: 500;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Utility Classes */
.text-success {
    color: var(--success-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.bg-success-light {
    background-color: rgba(40, 167, 69, 0.1) !important;
}

.bg-danger-light {
    background-color: rgba(220, 53, 69, 0.1) !important;
}

.border-success {
    border-color: var(--success-color) !important;
}

.border-danger {
    border-color: var(--danger-color) !important;
}
