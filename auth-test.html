<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kotak Neo API Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Kotak Neo API Authentication Test</h1>
        <p>This page will test the complete authentication flow with the Kotak Neo API.</p>
        
        <div class="form-group">
            <label for="consumerKey">Consumer Key:</label>
            <input type="text" id="consumerKey" placeholder="Enter your Consumer Key">
        </div>
        
        <div class="form-group">
            <label for="consumerSecret">Consumer Secret:</label>
            <input type="password" id="consumerSecret" placeholder="Enter your Consumer Secret">
        </div>
        
        <div class="form-group">
            <label for="mobileNumber">Mobile Number:</label>
            <input type="text" id="mobileNumber" placeholder="Enter mobile number (with +91)">
        </div>

        <div class="form-group">
            <label for="ucc">UCC:</label>
            <input type="text" id="ucc" placeholder="Enter your UCC">
        </div>

        <div class="form-group">
            <label for="totp">TOTP:</label>
            <input type="text" id="totp" placeholder="Enter 6-digit TOTP" maxlength="6">
        </div>

        <div class="form-group">
            <label for="mpin">MPIN:</label>
            <input type="password" id="mpin" placeholder="Enter 4-digit MPIN" maxlength="4">
        </div>
        
        <button onclick="testAuthentication()" id="testBtn">🚀 Test Authentication</button>
        <button onclick="testOAuthOnly()" id="oauthBtn">🔑 Test OAuth Only</button>
        <button onclick="clearResults()" id="clearBtn">🗑️ Clear Results</button>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Testing authentication...</p>
        </div>
        
        <div id="results"></div>
    </div>

    <!-- Include the trading bot scripts -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>

    <script>
        let testResults = [];

        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('testBtn').disabled = true;
            document.getElementById('oauthBtn').disabled = true;
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('testBtn').disabled = false;
            document.getElementById('oauthBtn').disabled = false;
        }

        function addResult(type, title, content) {
            testResults.push({ type, title, content, timestamp: new Date() });
            updateResultsDisplay();
        }

        function updateResultsDisplay() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '';
            
            testResults.forEach((result, index) => {
                const resultDiv = document.createElement('div');
                resultDiv.className = `result ${result.type}`;
                resultDiv.innerHTML = `
                    <strong>${result.timestamp.toLocaleTimeString()} - ${result.title}</strong>
                    ${result.content}
                `;
                resultsDiv.appendChild(resultDiv);
            });
            
            // Scroll to bottom
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            testResults = [];
            updateResultsDisplay();
        }

        async function testOAuthOnly() {
            const consumerKey = document.getElementById('consumerKey').value.trim();
            const consumerSecret = document.getElementById('consumerSecret').value.trim();
            
            if (!consumerKey || !consumerSecret) {
                addResult('error', 'Validation Error', 'Please enter Consumer Key and Consumer Secret');
                return;
            }
            
            showLoading();
            addResult('info', 'Starting OAuth Test', 'Testing OAuth2 token generation...');
            
            try {
                const api = new KotakNeoAPI();
                const result = await api.getAccessToken(consumerKey, consumerSecret);
                
                if (result.success) {
                    addResult('success', 'OAuth Success', `Access token obtained successfully!\nToken: ${result.data.access_token.substring(0, 50)}...\nExpires in: ${result.data.expires_in} seconds`);
                } else {
                    addResult('error', 'OAuth Failed', `Error: ${result.error}`);
                }
            } catch (error) {
                addResult('error', 'OAuth Exception', `Exception: ${error.message}`);
            }
            
            hideLoading();
        }

        async function testAuthentication() {
            const consumerKey = document.getElementById('consumerKey').value.trim();
            const consumerSecret = document.getElementById('consumerSecret').value.trim();
            const mobileNumber = document.getElementById('mobileNumber').value.trim();
            const ucc = document.getElementById('ucc').value.trim();
            const totp = document.getElementById('totp').value.trim();
            const mpin = document.getElementById('mpin').value.trim();

            // Validation
            if (!consumerKey || !consumerSecret || !mobileNumber || !ucc || !totp || !mpin) {
                addResult('error', 'Validation Error', 'Please fill in all fields');
                return;
            }

            if (!/^\+91\d{10}$/.test(mobileNumber) && !/^\d{10}$/.test(mobileNumber)) {
                addResult('error', 'Validation Error', 'Please enter a valid mobile number (with or without +91)');
                return;
            }

            if (!/^\d{6}$/.test(totp)) {
                addResult('error', 'Validation Error', 'Please enter a valid 6-digit TOTP');
                return;
            }

            if (!/^\d{4}$/.test(mpin)) {
                addResult('error', 'Validation Error', 'Please enter a valid 4-digit MPIN');
                return;
            }

            // Ensure mobile number has +91 prefix
            const formattedMobile = mobileNumber.startsWith('+91') ? mobileNumber : `+91${mobileNumber}`;
            
            showLoading();
            addResult('info', 'Starting Authentication Test', 'Testing complete TOTP authentication flow...');

            try {
                const api = new KotakNeoAPI();

                // Step 1: Test OAuth
                addResult('info', 'Step 1', 'Getting OAuth2 access token...');
                const oauthResult = await api.getAccessToken(consumerKey, consumerSecret);

                if (!oauthResult.success) {
                    addResult('error', 'OAuth Failed', `Error: ${oauthResult.error}`);
                    hideLoading();
                    return;
                }

                addResult('success', 'OAuth Success', `Access token obtained: ${oauthResult.data.access_token.substring(0, 50)}...`);

                // Step 2: Test TOTP Login
                addResult('info', 'Step 2', 'Logging in with TOTP...');
                const totpResult = await api.loginWithTOTP(formattedMobile, ucc, totp);

                if (!totpResult.success) {
                    addResult('error', 'TOTP Login Failed', `Error: ${totpResult.error}`);
                    hideLoading();
                    return;
                }

                addResult('success', 'TOTP Login Success', `View token obtained!\nSession ID: ${totpResult.data.sid}\nUCC: ${totpResult.data.ucc}\nGreeting: ${totpResult.data.greetingName}`);

                // Step 3: Test MPIN Validation
                addResult('info', 'Step 3', 'Validating with MPIN...');
                const mpinResult = await api.validateWithMPIN(mpin);

                if (!mpinResult.success) {
                    addResult('error', 'MPIN Validation Failed', `Error: ${mpinResult.error}`);
                    hideLoading();
                    return;
                }

                addResult('success', 'MPIN Validation Success', `Trade token obtained!\nSession ID: ${mpinResult.data.sid}\nToken Type: ${mpinResult.data.kType}\nGreeting: ${mpinResult.data.greetingName}`);

                // Step 4: Test API Call
                addResult('info', 'Step 4', 'Testing API call with trade token...');
                try {
                    const limitsResult = await api.makeRequest(CONFIG.API.ENDPOINTS.LIMITS);
                    addResult('success', 'API Test Success', `API call successful!\nResponse: ${JSON.stringify(limitsResult, null, 2)}`);
                } catch (apiError) {
                    addResult('error', 'API Test Failed', `API call failed: ${apiError.message}`);
                }

                // Complete login flow
                addResult('info', 'Step 5', 'Testing complete login flow...');
                const loginResult = await api.login(formattedMobile, ucc, totp, mpin, consumerKey, consumerSecret);

                if (loginResult.success) {
                    addResult('success', 'Complete Login Success', `Full TOTP authentication successful!\nUser ID: ${loginResult.data.userId}\nUCC: ${loginResult.data.ucc}\nAccess Token: ${loginResult.data.accessToken.substring(0, 50)}...\nView Token: ${loginResult.data.viewToken.substring(0, 50)}...\nTrade Token: ${loginResult.data.tradeToken.substring(0, 50)}...\nGreeting: ${loginResult.data.greetingName}`);
                } else {
                    addResult('error', 'Complete Login Failed', `Error: ${loginResult.error}`);
                }
                
            } catch (error) {
                addResult('error', 'Authentication Exception', `Exception: ${error.message}\nStack: ${error.stack}`);
            }
            
            hideLoading();
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addResult('info', 'Test Page Loaded', 'Ready to test Kotak Neo API authentication.\n\nSteps:\n1. Enter your API credentials\n2. Click "Test Authentication" for full test\n3. Or click "Test OAuth Only" for just token generation\n\nMake sure you have valid Kotak Neo API credentials.');
        });
    </script>
</body>
</html>
