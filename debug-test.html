<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test - Login Button</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin: 10px 0;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Debug Test - Login Button</h1>
        <p>This page tests if the login button and JavaScript are working properly.</p>
        
        <div class="form-group">
            <label for="consumerKey">Consumer Key:</label>
            <input type="text" id="consumerKey" placeholder="Test Consumer Key">
        </div>
        
        <div class="form-group">
            <label for="consumerSecret">Consumer Secret:</label>
            <input type="password" id="consumerSecret" placeholder="Test Consumer Secret">
        </div>
        
        <div class="form-group">
            <label for="mobileNumber">Mobile Number:</label>
            <input type="text" id="mobileNumber" placeholder="Test Mobile Number">
        </div>
        
        <div class="form-group">
            <label for="ucc">UCC:</label>
            <input type="text" id="ucc" placeholder="Test UCC">
        </div>
        
        <div class="form-group">
            <label for="totp">TOTP:</label>
            <input type="text" id="totp" placeholder="Test TOTP">
        </div>
        
        <div class="form-group">
            <label for="mpin">MPIN:</label>
            <input type="password" id="mpin" placeholder="Test MPIN">
        </div>
        
        <button id="testBtn1">🧪 Test Button 1 (Direct onclick)</button>
        <button id="testBtn2">🧪 Test Button 2 (addEventListener)</button>
        <button id="loginBtn">🔐 Login Button (Same as main app)</button>
        
        <div class="log" id="logOutput">
            <strong>Debug Log:</strong><br>
            Page loaded...<br>
        </div>
    </div>

    <!-- Include the same scripts as main app -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/websocket.js"></script>
    <script src="js/trading.js"></script>
    <script src="js/scheduler.js"></script>
    <script src="js/app.js"></script>

    <script>
        function log(message) {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            logOutput.innerHTML += `${timestamp}: ${message}<br>`;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(message);
        }

        // Test 1: Direct onclick
        document.getElementById('testBtn1').onclick = function() {
            log('✅ Test Button 1 clicked - Direct onclick works!');
        };

        // Test 2: addEventListener
        document.getElementById('testBtn2').addEventListener('click', function() {
            log('✅ Test Button 2 clicked - addEventListener works!');
        });

        // Test 3: Same setup as main app
        document.addEventListener('DOMContentLoaded', function() {
            log('✅ DOMContentLoaded event fired');
            
            try {
                // Test if classes exist
                log('Testing if classes exist...');
                
                if (typeof CONFIG !== 'undefined') {
                    log('✅ CONFIG object exists');
                } else {
                    log('❌ CONFIG object missing');
                }
                
                if (typeof KotakNeoAPI !== 'undefined') {
                    log('✅ KotakNeoAPI class exists');
                } else {
                    log('❌ KotakNeoAPI class missing');
                }
                
                if (typeof TradingBotApp !== 'undefined') {
                    log('✅ TradingBotApp class exists');
                } else {
                    log('❌ TradingBotApp class missing');
                }
                
                // Test login button
                const loginBtn = document.getElementById('loginBtn');
                if (loginBtn) {
                    log('✅ Login button found');
                    
                    loginBtn.addEventListener('click', function() {
                        log('✅ Login button clicked via addEventListener!');
                        
                        // Test form values
                        const consumerKey = document.getElementById('consumerKey').value.trim();
                        const consumerSecret = document.getElementById('consumerSecret').value.trim();
                        const mobileNumber = document.getElementById('mobileNumber').value.trim();
                        const ucc = document.getElementById('ucc').value.trim();
                        const totp = document.getElementById('totp').value.trim();
                        const mpin = document.getElementById('mpin').value.trim();
                        
                        log(`Form values: CK=${consumerKey ? '[SET]' : '[EMPTY]'}, CS=${consumerSecret ? '[SET]' : '[EMPTY]'}, Mobile=${mobileNumber}, UCC=${ucc}, TOTP=${totp}, MPIN=${mpin}`);
                        
                        if (!consumerKey || !consumerSecret || !mobileNumber || !ucc || !totp || !mpin) {
                            log('❌ Please fill in all fields');
                            alert('Please fill in all fields');
                            return;
                        }
                        
                        log('✅ All fields filled - would proceed with authentication');
                        alert('Debug test successful! All fields filled.');
                    });
                    
                    log('✅ Login button event listener attached');
                } else {
                    log('❌ Login button not found');
                }
                
                // Test if TradingBotApp can be instantiated
                try {
                    log('Testing TradingBotApp instantiation...');
                    // Don't actually create it to avoid conflicts
                    log('✅ TradingBotApp class is available for instantiation');
                } catch (error) {
                    log('❌ Error with TradingBotApp: ' + error.message);
                }
                
            } catch (error) {
                log('❌ Error in DOMContentLoaded: ' + error.message);
                console.error('Error:', error);
            }
        });
        
        log('✅ Debug script loaded');
    </script>
</body>
</html>
