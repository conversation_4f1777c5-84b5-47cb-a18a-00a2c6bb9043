# 🚀 Quick Start Guide

## ⚡ Start Your Trading Bot in 3 Steps

### Step 1: Install Dependencies
```bash
npm install
```

### Step 2: Start Server
```bash
npm start
```

### Step 3: Open Browser
Go to: **http://localhost:8000**

---

## 🎯 Alternative Methods

### Windows Users:
- **Double-click**: `start-server.bat`
- **Command**: `npm start`

### Mac/Linux Users:
- **Terminal**: `chmod +x start-server.sh && ./start-server.sh`
- **Command**: `npm start`

---

## 🔐 Login Process

1. **Enter Credentials:**
   - Consumer Key
   - Consumer Secret
   - Mobile Number (+91xxxxxxxxxx)
   - UCC
   - TOTP (6-digit from authenticator app)
   - MPIN (4-6 digits)

2. **Click Login**

3. **Start Trading!**

---

## ✅ What's Fixed

- ✅ **CORS Issues Resolved** - No more API blocking
- ✅ **Authentication Working** - Complete TOTP flow
- ✅ **All Features Enabled** - Trading, scheduling, monitoring
- ✅ **Real-time Data** - Live market feeds
- ✅ **Auto Browser Opening** - Server opens browser automatically

---

## 🔧 If Something Goes Wrong

### Port Busy:
```bash
PORT=8080 npm start
```

### Dependencies Missing:
```bash
npm install
```

### Browser Not Opening:
Manually go to: `http://localhost:8000`

---

## 📞 Ready to Trade!

Your bot is now fully operational with:
- ✅ Kotak Neo API integration
- ✅ TOTP authentication
- ✅ Real-time market data
- ✅ Automated trading features
- ✅ Risk management tools
