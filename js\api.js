// Kotak Neo API Integration Layer
class KotakNeoAPI {
    constructor() {
        this.baseURL = CONFIG.API.BASE_URL;
        this.oauthURL = CONFIG.API.OAUTH_URL;
        this.headers = { ...CONFIG.API.HEADERS };
        this.isAuthenticated = false;
        this.accessToken = null;
        this.sessionToken = null;
        this.consumerKey = null;
        this.consumerSecret = null;
        this.userId = null;
        this.retryCount = 0;
        this.maxRetries = CONFIG.ERROR_HANDLING.MAX_RETRY_ATTEMPTS;

        // Initialize from stored credentials if available
        this.loadStoredCredentials();
    }
    
    // Load stored credentials from localStorage
    loadStoredCredentials() {
        try {
            const stored = localStorage.getItem(CONFIG.STORAGE.STORAGE_PREFIX + CONFIG.STORAGE.KEYS.USER_CREDENTIALS);
            if (stored) {
                const credentials = JSON.parse(stored);
                this.accessToken = credentials.accessToken;
                this.sessionToken = credentials.sessionToken;
                this.consumerKey = credentials.consumerKey;
                this.consumerSecret = credentials.consumerSecret;
                this.userId = credentials.userId;
                this.isAuthenticated = credentials.isAuthenticated;

                if (this.isAuthenticated && this.accessToken) {
                    this.updateHeaders();
                }
            }
        } catch (error) {
            console.error('Error loading stored credentials:', error);
        }
    }

    // Save credentials to localStorage
    saveCredentials() {
        try {
            const credentials = {
                accessToken: this.accessToken,
                sessionToken: this.sessionToken,
                consumerKey: this.consumerKey,
                consumerSecret: this.consumerSecret,
                userId: this.userId,
                isAuthenticated: this.isAuthenticated
            };
            localStorage.setItem(
                CONFIG.STORAGE.STORAGE_PREFIX + CONFIG.STORAGE.KEYS.USER_CREDENTIALS,
                JSON.stringify(credentials)
            );
        } catch (error) {
            console.error('Error saving credentials:', error);
        }
    }

    // Update request headers with authentication info
    updateHeaders() {
        if (this.accessToken) {
            this.headers.Authorization = `Bearer ${this.accessToken}`;
        }
        if (this.consumerKey) {
            this.headers.consumerKey = this.consumerKey;
        }
        if (this.sessionToken) {
            this.headers.sid = this.sessionToken;
        }
    }
    
    // Generic API request method with error handling and retry logic
    async makeRequest(endpoint, method = 'GET', data = null, customHeaders = {}) {
        const url = `${this.baseURL}${endpoint}`;

        // Ensure we have proper authentication headers
        this.updateHeaders();

        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            ...this.headers,
            ...customHeaders
        };

        const requestOptions = {
            method,
            headers,
            mode: 'cors',
            credentials: 'include'
        };

        if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
            requestOptions.body = JSON.stringify(data);
        }

        try {
            Logger.log('API Request', { url, method, headers: this.sanitizeHeaders(headers), data });

            const response = await fetch(url, requestOptions);

            let responseData;
            try {
                responseData = await response.json();
            } catch (parseError) {
                responseData = { message: 'Invalid JSON response', status: response.status };
            }

            if (!response.ok) {
                // Handle authentication errors
                if (response.status === 401 || response.status === 403) {
                    this.isAuthenticated = false;
                    this.accessToken = null;
                    this.sessionToken = null;
                    localStorage.removeItem(CONFIG.STORAGE.STORAGE_PREFIX + CONFIG.STORAGE.KEYS.USER_CREDENTIALS);
                }

                throw new Error(`API Error: ${response.status} - ${responseData.emsg || responseData.message || 'Unknown error'}`);
            }

            Logger.log('API Response', responseData);
            this.retryCount = 0; // Reset retry count on success

            return responseData;
        } catch (error) {
            Logger.error('API Request Failed', error);

            // Retry logic for network errors (but not auth errors)
            if (this.retryCount < this.maxRetries && this.isRetryableError(error) && !error.message.includes('401') && !error.message.includes('403')) {
                this.retryCount++;
                Logger.log(`Retrying request (${this.retryCount}/${this.maxRetries})`);

                await this.delay(CONFIG.ERROR_HANDLING.RETRY_DELAY * this.retryCount);
                return this.makeRequest(endpoint, method, data, customHeaders);
            }

            throw error;
        }
    }

    // Sanitize headers for logging (remove sensitive data)
    sanitizeHeaders(headers) {
        const sanitized = { ...headers };
        if (sanitized.Authorization) {
            sanitized.Authorization = 'Bearer [REDACTED]';
        }
        if (sanitized.consumerKey) {
            sanitized.consumerKey = '[REDACTED]';
        }
        return sanitized;
    }
    
    // Check if error is retryable
    isRetryableError(error) {
        const retryableErrors = ['NetworkError', 'TimeoutError', 'ConnectionError'];
        return retryableErrors.some(errorType => error.message.includes(errorType));
    }
    
    // Delay utility for retry logic
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // Authentication Methods
    async login(mobileNumber, password, consumerKey, consumerSecret) {
        try {
            // Step 1: Get OAuth2 Access Token
            const accessTokenResult = await this.getAccessToken(consumerKey, consumerSecret);
            if (!accessTokenResult.success) {
                throw new Error(accessTokenResult.error);
            }

            // Step 2: Login with user credentials to get session token
            const sessionResult = await this.createSession(mobileNumber, password);
            if (!sessionResult.success) {
                throw new Error(sessionResult.error);
            }

            // Store credentials
            this.consumerKey = consumerKey;
            this.consumerSecret = consumerSecret;
            this.userId = mobileNumber;
            this.isAuthenticated = true;

            this.updateHeaders();
            this.saveCredentials();

            Logger.log('Login successful');
            return {
                success: true,
                data: {
                    accessToken: this.accessToken,
                    sessionToken: this.sessionToken,
                    userId: this.userId
                }
            };
        } catch (error) {
            Logger.error('Login failed', error);
            return { success: false, error: error.message };
        }
    }

    // Step 1: Get OAuth2 Access Token using Client Credentials
    async getAccessToken(consumerKey, consumerSecret) {
        try {
            const tokenData = new URLSearchParams({
                grant_type: 'client_credentials',
                client_id: consumerKey,
                client_secret: consumerSecret
            });

            const response = await fetch(this.oauthURL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Accept': 'application/json'
                },
                body: tokenData
            });

            const data = await response.json();

            if (response.ok && data.access_token) {
                this.accessToken = data.access_token;
                Logger.log('OAuth2 access token obtained');
                return { success: true, data: data };
            } else {
                throw new Error(data.error_description || data.error || 'Failed to get access token');
            }
        } catch (error) {
            Logger.error('OAuth2 token request failed', error);
            return { success: false, error: error.message };
        }
    }

    // Step 2: Create Session with User Credentials
    async createSession(mobileNumber, password) {
        try {
            if (!this.accessToken) {
                throw new Error('Access token required for session creation');
            }

            const sessionData = {
                mobileNumber: mobileNumber,
                password: password
            };

            const headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': `Bearer ${this.accessToken}`,
                'consumerKey': this.consumerKey
            };

            const response = await fetch(`${this.baseURL}${CONFIG.API.ENDPOINTS.LOGIN}`, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(sessionData)
            });

            const data = await response.json();

            if (response.ok && data.stat === 'Ok') {
                this.sessionToken = data.data.sid || data.data.sessionToken;
                Logger.log('Session created successfully');
                return { success: true, data: data.data };
            } else {
                throw new Error(data.emsg || data.message || 'Session creation failed');
            }
        } catch (error) {
            Logger.error('Session creation failed', error);
            return { success: false, error: error.message };
        }
    }
    
    async logout() {
        try {
            if (this.isAuthenticated && this.sessionToken) {
                // Call logout endpoint to invalidate session
                await this.makeRequest(CONFIG.API.ENDPOINTS.LOGOUT, 'POST');
            }

            // Clear all authentication data
            this.accessToken = null;
            this.sessionToken = null;
            this.consumerKey = null;
            this.consumerSecret = null;
            this.userId = null;
            this.isAuthenticated = false;

            // Clear stored credentials
            localStorage.removeItem(CONFIG.STORAGE.STORAGE_PREFIX + CONFIG.STORAGE.KEYS.USER_CREDENTIALS);

            // Reset headers
            this.headers = { ...CONFIG.API.HEADERS };

            Logger.log('Logout successful');
            return { success: true };
        } catch (error) {
            Logger.error('Logout failed', error);
            // Even if logout API fails, clear local data
            this.accessToken = null;
            this.sessionToken = null;
            this.consumerKey = null;
            this.consumerSecret = null;
            this.userId = null;
            this.isAuthenticated = false;
            localStorage.removeItem(CONFIG.STORAGE.STORAGE_PREFIX + CONFIG.STORAGE.KEYS.USER_CREDENTIALS);
            this.headers = { ...CONFIG.API.HEADERS };

            return { success: false, error: error.message };
        }
    }
    
    // Order Management Methods
    async placeOrder(orderData) {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(CONFIG.API.ENDPOINTS.PLACE_ORDER, 'POST', orderData);
            
            if (response.stat === 'Ok') {
                Logger.log('Order placed successfully', response.data);
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Order placement failed');
            }
        } catch (error) {
            Logger.error('Order placement failed', error);
            return { success: false, error: error.message };
        }
    }
    
    async modifyOrder(orderId, modifyData) {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(
                `${CONFIG.API.ENDPOINTS.MODIFY_ORDER}/${orderId}`,
                'PUT',
                modifyData
            );
            
            if (response.stat === 'Ok') {
                Logger.log('Order modified successfully', response.data);
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Order modification failed');
            }
        } catch (error) {
            Logger.error('Order modification failed', error);
            return { success: false, error: error.message };
        }
    }
    
    async cancelOrder(orderId) {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(
                `${CONFIG.API.ENDPOINTS.CANCEL_ORDER}/${orderId}`,
                'DELETE'
            );
            
            if (response.stat === 'Ok') {
                Logger.log('Order cancelled successfully', response.data);
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Order cancellation failed');
            }
        } catch (error) {
            Logger.error('Order cancellation failed', error);
            return { success: false, error: error.message };
        }
    }
    
    // Data Retrieval Methods
    async getOrderBook() {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(CONFIG.API.ENDPOINTS.ORDER_BOOK);
            
            if (response.stat === 'Ok') {
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Failed to fetch order book');
            }
        } catch (error) {
            Logger.error('Failed to fetch order book', error);
            return { success: false, error: error.message };
        }
    }
    
    async getPositions() {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(CONFIG.API.ENDPOINTS.POSITIONS);
            
            if (response.stat === 'Ok') {
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Failed to fetch positions');
            }
        } catch (error) {
            Logger.error('Failed to fetch positions', error);
            return { success: false, error: error.message };
        }
    }
    
    async getHoldings() {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(CONFIG.API.ENDPOINTS.HOLDINGS);
            
            if (response.stat === 'Ok') {
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Failed to fetch holdings');
            }
        } catch (error) {
            Logger.error('Failed to fetch holdings', error);
            return { success: false, error: error.message };
        }
    }
    
    async getLimits() {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(CONFIG.API.ENDPOINTS.LIMITS);
            
            if (response.stat === 'Ok') {
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Failed to fetch limits');
            }
        } catch (error) {
            Logger.error('Failed to fetch limits', error);
            return { success: false, error: error.message };
        }
    }
    
    async getQuote(symbol, exchange) {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(
                `${CONFIG.API.ENDPOINTS.QUOTE}?symbol=${symbol}&exchange=${exchange}`
            );
            
            if (response.stat === 'Ok') {
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Failed to fetch quote');
            }
        } catch (error) {
            Logger.error('Failed to fetch quote', error);
            return { success: false, error: error.message };
        }
    }
    
    async searchScrip(searchText, exchange) {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(
                `${CONFIG.API.ENDPOINTS.SEARCH_SCRIP}?stext=${searchText}&exch=${exchange}`
            );
            
            if (response.stat === 'Ok') {
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Failed to search scrip');
            }
        } catch (error) {
            Logger.error('Failed to search scrip', error);
            return { success: false, error: error.message };
        }
    }
}

}

// Logger utility class
class Logger {
    static log(message, data = null) {
        if (CONFIG.LOGGING.CONSOLE_LOGGING) {
            console.log(`[${new Date().toISOString()}] ${message}`, data || '');
        }
    }

    static error(message, error = null) {
        if (CONFIG.LOGGING.CONSOLE_LOGGING) {
            console.error(`[${new Date().toISOString()}] ERROR: ${message}`, error || '');
        }
    }

    static warn(message, data = null) {
        if (CONFIG.LOGGING.CONSOLE_LOGGING) {
            console.warn(`[${new Date().toISOString()}] WARNING: ${message}`, data || '');
        }
    }
}

// Create global API instance
const kotakAPI = new KotakNeoAPI();
