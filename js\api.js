// Kotak Neo API Integration Layer
class KotakNeoAPI {
    constructor() {
        this.baseURL = CONFIG.API.BASE_URL;
        this.oauthURL = CONFIG.API.OAUTH_URL;
        this.headers = { ...CONFIG.API.HEADERS };
        this.isAuthenticated = false;

        // Authentication tokens
        this.accessToken = null;      // OAuth2 token
        this.viewToken = null;        // View token from TOTP login
        this.tradeToken = null;       // Trade token from MPIN validation
        this.sessionToken = null;     // Session ID

        // User credentials
        this.consumerKey = null;
        this.consumerSecret = null;
        this.userId = null;
        this.ucc = null;
        this.rid = null;

        this.retryCount = 0;
        this.maxRetries = CONFIG.ERROR_HANDLING.MAX_RETRY_ATTEMPTS;

        // Initialize from stored credentials if available
        this.loadStoredCredentials();
    }
    
    // Load stored credentials from localStorage
    loadStoredCredentials() {
        try {
            const stored = localStorage.getItem(CONFIG.STORAGE.STORAGE_PREFIX + CONFIG.STORAGE.KEYS.USER_CREDENTIALS);
            if (stored) {
                const credentials = JSON.parse(stored);
                this.accessToken = credentials.accessToken;
                this.viewToken = credentials.viewToken;
                this.tradeToken = credentials.tradeToken;
                this.sessionToken = credentials.sessionToken;
                this.consumerKey = credentials.consumerKey;
                this.consumerSecret = credentials.consumerSecret;
                this.userId = credentials.userId;
                this.ucc = credentials.ucc;
                this.rid = credentials.rid;
                this.isAuthenticated = credentials.isAuthenticated;

                if (this.isAuthenticated && this.tradeToken) {
                    this.updateHeaders();
                }
            }
        } catch (error) {
            console.error('Error loading stored credentials:', error);
        }
    }

    // Save credentials to localStorage
    saveCredentials() {
        try {
            const credentials = {
                accessToken: this.accessToken,
                viewToken: this.viewToken,
                tradeToken: this.tradeToken,
                sessionToken: this.sessionToken,
                consumerKey: this.consumerKey,
                consumerSecret: this.consumerSecret,
                userId: this.userId,
                ucc: this.ucc,
                rid: this.rid,
                isAuthenticated: this.isAuthenticated
            };
            localStorage.setItem(
                CONFIG.STORAGE.STORAGE_PREFIX + CONFIG.STORAGE.KEYS.USER_CREDENTIALS,
                JSON.stringify(credentials)
            );
        } catch (error) {
            console.error('Error saving credentials:', error);
        }
    }

    // Update request headers with authentication info
    updateHeaders() {
        // Always add neo-fin-key for Kotak Neo API
        this.headers['neo-fin-key'] = 'neotradeapi';

        // Add access token if available
        if (this.accessToken) {
            this.headers.Authorization = `Bearer ${this.accessToken}`;
        }

        // Add trade token and session for authenticated calls
        if (this.tradeToken) {
            this.headers.Auth = this.tradeToken;
            this.headers.sid = this.sessionToken;
        }
    }
    
    // Generic API request method with error handling and retry logic
    async makeRequest(endpoint, method = 'GET', data = null, customHeaders = {}) {
        const url = `${this.baseURL}${endpoint}`;

        // Ensure we have proper authentication headers
        this.updateHeaders();

        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            ...this.headers,
            ...customHeaders
        };

        const requestOptions = {
            method,
            headers,
            mode: 'cors',
            credentials: 'include'
        };

        if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
            requestOptions.body = JSON.stringify(data);
        }

        try {
            Logger.log('API Request', { url, method, headers: this.sanitizeHeaders(headers), data });

            const response = await fetch(url, requestOptions);

            let responseData;
            try {
                responseData = await response.json();
            } catch (parseError) {
                responseData = { message: 'Invalid JSON response', status: response.status };
            }

            if (!response.ok) {
                // Handle authentication errors
                if (response.status === 401 || response.status === 403) {
                    this.isAuthenticated = false;
                    this.accessToken = null;
                    this.sessionToken = null;
                    localStorage.removeItem(CONFIG.STORAGE.STORAGE_PREFIX + CONFIG.STORAGE.KEYS.USER_CREDENTIALS);
                }

                throw new Error(`API Error: ${response.status} - ${responseData.emsg || responseData.message || 'Unknown error'}`);
            }

            Logger.log('API Response', responseData);
            this.retryCount = 0; // Reset retry count on success

            return responseData;
        } catch (error) {
            Logger.error('API Request Failed', error);

            // Retry logic for network errors (but not auth errors)
            if (this.retryCount < this.maxRetries && this.isRetryableError(error) && !error.message.includes('401') && !error.message.includes('403')) {
                this.retryCount++;
                Logger.log(`Retrying request (${this.retryCount}/${this.maxRetries})`);

                await this.delay(CONFIG.ERROR_HANDLING.RETRY_DELAY * this.retryCount);
                return this.makeRequest(endpoint, method, data, customHeaders);
            }

            throw error;
        }
    }

    // Sanitize headers for logging (remove sensitive data)
    sanitizeHeaders(headers) {
        const sanitized = { ...headers };
        if (sanitized.Authorization) {
            sanitized.Authorization = 'Bearer [REDACTED]';
        }
        if (sanitized.consumerKey) {
            sanitized.consumerKey = '[REDACTED]';
        }
        return sanitized;
    }
    
    // Check if error is retryable
    isRetryableError(error) {
        const retryableErrors = ['NetworkError', 'TimeoutError', 'ConnectionError'];
        return retryableErrors.some(errorType => error.message.includes(errorType));
    }
    
    // Delay utility for retry logic
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // Authentication Methods - Complete TOTP Flow
    async login(mobileNumber, ucc, totp, mpin, consumerKey, consumerSecret) {
        try {
            Logger.log('Starting Kotak Neo authentication flow...');

            // Step 1: Get OAuth2 Access Token
            Logger.log('Step 1: Getting OAuth2 access token...');
            const accessTokenResult = await this.getAccessToken(consumerKey, consumerSecret);
            if (!accessTokenResult.success) {
                throw new Error(`OAuth failed: ${accessTokenResult.error}`);
            }

            // Step 2: Login with TOTP to get View Token
            Logger.log('Step 2: Logging in with TOTP...');
            const totpResult = await this.loginWithTOTP(mobileNumber, ucc, totp);
            if (!totpResult.success) {
                throw new Error(`TOTP login failed: ${totpResult.error}`);
            }

            // Step 3: Validate with MPIN to get Trade Token
            Logger.log('Step 3: Validating with MPIN...');
            const mpinResult = await this.validateWithMPIN(mpin);
            if (!mpinResult.success) {
                throw new Error(`MPIN validation failed: ${mpinResult.error}`);
            }

            // Store credentials
            this.consumerKey = consumerKey;
            this.consumerSecret = consumerSecret;
            this.userId = mobileNumber;
            this.ucc = ucc;
            this.isAuthenticated = true;

            this.updateHeaders();
            this.saveCredentials();

            Logger.log('Complete authentication successful');
            return {
                success: true,
                data: {
                    accessToken: this.accessToken,
                    viewToken: this.viewToken,
                    tradeToken: this.tradeToken,
                    sessionId: this.sessionToken,
                    userId: this.userId,
                    ucc: this.ucc,
                    greetingName: mpinResult.data.greetingName
                }
            };
        } catch (error) {
            Logger.error('Authentication failed', error);
            return { success: false, error: error.message };
        }
    }

    // Step 1: Get OAuth2 Access Token using Basic Auth (as per documentation)
    async getAccessToken(consumerKey, consumerSecret) {
        try {
            // Create Basic Auth header (Base64 encode consumerKey:consumerSecret)
            const credentials = btoa(`${consumerKey}:${consumerSecret}`);

            const tokenData = new URLSearchParams({
                grant_type: 'client_credentials'
            });

            const response = await fetch(this.oauthURL, {
                method: 'POST',
                headers: {
                    'Authorization': `Basic ${credentials}`,
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Accept': 'application/json'
                },
                body: tokenData
            });

            const data = await response.json();

            Logger.log('OAuth2 Response:', data);

            if (response.ok && data.access_token) {
                this.accessToken = data.access_token;
                Logger.log('OAuth2 access token obtained successfully');
                return { success: true, data: data };
            } else {
                throw new Error(data.error_description || data.error || `HTTP ${response.status}: Failed to get access token`);
            }
        } catch (error) {
            Logger.error('OAuth2 token request failed', error);
            return { success: false, error: error.message };
        }
    }

    // Step 2: Login with TOTP to get View Token (as per documentation)
    async loginWithTOTP(mobileNumber, ucc, totp) {
        try {
            if (!this.accessToken) {
                throw new Error('Access token required for TOTP login');
            }

            const loginData = {
                mobileNumber: mobileNumber,
                ucc: ucc,
                totp: totp
            };

            const headers = {
                'Authorization': `Bearer ${this.accessToken}`,
                'neo-fin-key': 'neotradeapi',
                'Content-Type': 'application/json'
            };

            const response = await fetch(`${this.baseURL}${CONFIG.API.ENDPOINTS.LOGIN_TOTP}`, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(loginData)
            });

            const data = await response.json();

            Logger.log('TOTP Login Response:', data);

            if (response.ok && data.data && data.data.token) {
                this.viewToken = data.data.token;
                this.sessionToken = data.data.sid;
                this.rid = data.data.rid;
                Logger.log('TOTP login successful - View token obtained');
                return { success: true, data: data.data };
            } else {
                throw new Error(data.emsg || data.message || `HTTP ${response.status}: TOTP login failed`);
            }
        } catch (error) {
            Logger.error('TOTP login failed', error);
            return { success: false, error: error.message };
        }
    }

    // Step 3: Validate with MPIN to get Trade Token (as per documentation)
    async validateWithMPIN(mpin) {
        try {
            if (!this.accessToken || !this.viewToken || !this.sessionToken) {
                throw new Error('Access token, view token, and session ID required for MPIN validation');
            }

            const mpinData = {
                mpin: mpin
            };

            const headers = {
                'accept': 'application/json',
                'sid': this.sessionToken,
                'Auth': this.viewToken,
                'neo-fin-key': 'neotradeapi',
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.accessToken}`
            };

            const response = await fetch(`${this.baseURL}${CONFIG.API.ENDPOINTS.LOGIN_VALIDATE}`, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(mpinData)
            });

            const data = await response.json();

            Logger.log('MPIN Validation Response:', data);

            if (response.ok && data.data && data.data.token) {
                this.tradeToken = data.data.token;
                this.sessionToken = data.data.sid; // Update with new session ID
                Logger.log('MPIN validation successful - Trade token obtained');
                return { success: true, data: data.data };
            } else {
                throw new Error(data.emsg || data.message || `HTTP ${response.status}: MPIN validation failed`);
            }
        } catch (error) {
            Logger.error('MPIN validation failed', error);
            return { success: false, error: error.message };
        }
    }
    
    async logout() {
        try {
            if (this.isAuthenticated && this.sessionToken) {
                // Call logout endpoint to invalidate session
                await this.makeRequest(CONFIG.API.ENDPOINTS.LOGOUT, 'POST');
            }

            // Clear all authentication data
            this.accessToken = null;
            this.viewToken = null;
            this.tradeToken = null;
            this.sessionToken = null;
            this.consumerKey = null;
            this.consumerSecret = null;
            this.userId = null;
            this.ucc = null;
            this.rid = null;
            this.isAuthenticated = false;

            // Clear stored credentials
            localStorage.removeItem(CONFIG.STORAGE.STORAGE_PREFIX + CONFIG.STORAGE.KEYS.USER_CREDENTIALS);

            // Reset headers
            this.headers = { ...CONFIG.API.HEADERS };

            Logger.log('Logout successful');
            return { success: true };
        } catch (error) {
            Logger.error('Logout failed', error);
            // Even if logout API fails, clear local data
            this.accessToken = null;
            this.viewToken = null;
            this.tradeToken = null;
            this.sessionToken = null;
            this.consumerKey = null;
            this.consumerSecret = null;
            this.userId = null;
            this.ucc = null;
            this.rid = null;
            this.isAuthenticated = false;
            localStorage.removeItem(CONFIG.STORAGE.STORAGE_PREFIX + CONFIG.STORAGE.KEYS.USER_CREDENTIALS);
            this.headers = { ...CONFIG.API.HEADERS };

            return { success: false, error: error.message };
        }
    }
    
    // Order Management Methods
    async placeOrder(orderData) {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(CONFIG.API.ENDPOINTS.PLACE_ORDER, 'POST', orderData);
            
            if (response.stat === 'Ok') {
                Logger.log('Order placed successfully', response.data);
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Order placement failed');
            }
        } catch (error) {
            Logger.error('Order placement failed', error);
            return { success: false, error: error.message };
        }
    }
    
    async modifyOrder(orderId, modifyData) {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(
                `${CONFIG.API.ENDPOINTS.MODIFY_ORDER}/${orderId}`,
                'PUT',
                modifyData
            );
            
            if (response.stat === 'Ok') {
                Logger.log('Order modified successfully', response.data);
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Order modification failed');
            }
        } catch (error) {
            Logger.error('Order modification failed', error);
            return { success: false, error: error.message };
        }
    }
    
    async cancelOrder(orderId) {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(
                `${CONFIG.API.ENDPOINTS.CANCEL_ORDER}/${orderId}`,
                'DELETE'
            );
            
            if (response.stat === 'Ok') {
                Logger.log('Order cancelled successfully', response.data);
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Order cancellation failed');
            }
        } catch (error) {
            Logger.error('Order cancellation failed', error);
            return { success: false, error: error.message };
        }
    }
    
    // Data Retrieval Methods
    async getOrderBook() {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(CONFIG.API.ENDPOINTS.ORDER_BOOK);
            
            if (response.stat === 'Ok') {
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Failed to fetch order book');
            }
        } catch (error) {
            Logger.error('Failed to fetch order book', error);
            return { success: false, error: error.message };
        }
    }
    
    async getPositions() {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(CONFIG.API.ENDPOINTS.POSITIONS);
            
            if (response.stat === 'Ok') {
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Failed to fetch positions');
            }
        } catch (error) {
            Logger.error('Failed to fetch positions', error);
            return { success: false, error: error.message };
        }
    }
    
    async getHoldings() {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(CONFIG.API.ENDPOINTS.HOLDINGS);
            
            if (response.stat === 'Ok') {
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Failed to fetch holdings');
            }
        } catch (error) {
            Logger.error('Failed to fetch holdings', error);
            return { success: false, error: error.message };
        }
    }
    
    async getLimits() {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(CONFIG.API.ENDPOINTS.LIMITS);
            
            if (response.stat === 'Ok') {
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Failed to fetch limits');
            }
        } catch (error) {
            Logger.error('Failed to fetch limits', error);
            return { success: false, error: error.message };
        }
    }
    
    async getQuote(symbol, exchange) {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(
                `${CONFIG.API.ENDPOINTS.QUOTE}?symbol=${symbol}&exchange=${exchange}`
            );
            
            if (response.stat === 'Ok') {
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Failed to fetch quote');
            }
        } catch (error) {
            Logger.error('Failed to fetch quote', error);
            return { success: false, error: error.message };
        }
    }
    
    async searchScrip(searchText, exchange) {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(
                `${CONFIG.API.ENDPOINTS.SEARCH_SCRIP}?stext=${searchText}&exch=${exchange}`
            );
            
            if (response.stat === 'Ok') {
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Failed to search scrip');
            }
        } catch (error) {
            Logger.error('Failed to search scrip', error);
            return { success: false, error: error.message };
        }
    }
}

}

// Logger utility class
class Logger {
    static log(message, data = null) {
        if (CONFIG.LOGGING.CONSOLE_LOGGING) {
            console.log(`[${new Date().toISOString()}] ${message}`, data || '');
        }
    }

    static error(message, error = null) {
        if (CONFIG.LOGGING.CONSOLE_LOGGING) {
            console.error(`[${new Date().toISOString()}] ERROR: ${message}`, error || '');
        }
    }

    static warn(message, data = null) {
        if (CONFIG.LOGGING.CONSOLE_LOGGING) {
            console.warn(`[${new Date().toISOString()}] WARNING: ${message}`, data || '');
        }
    }
}

// Create global API instance
const kotakAPI = new KotakNeoAPI();
