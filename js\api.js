// Kotak Neo API Integration Layer
class KotakNeoAPI {
    constructor() {
        this.baseURL = CONFIG.API.BASE_URL;
        this.headers = { ...CONFIG.API.HEADERS };
        this.isAuthenticated = false;
        this.authToken = null;
        this.sessionId = null;
        this.consumerKey = null;
        this.retryCount = 0;
        this.maxRetries = CONFIG.ERROR_HANDLING.MAX_RETRY_ATTEMPTS;
        
        // Initialize from stored credentials if available
        this.loadStoredCredentials();
    }
    
    // Load stored credentials from localStorage
    loadStoredCredentials() {
        try {
            const stored = localStorage.getItem(CONFIG.STORAGE.STORAGE_PREFIX + CONFIG.STORAGE.KEYS.USER_CREDENTIALS);
            if (stored) {
                const credentials = JSON.parse(stored);
                this.authToken = credentials.authToken;
                this.sessionId = credentials.sessionId;
                this.consumerKey = credentials.consumerKey;
                this.isAuthenticated = credentials.isAuthenticated;
                
                if (this.isAuthenticated) {
                    this.updateHeaders();
                }
            }
        } catch (error) {
            console.error('Error loading stored credentials:', error);
        }
    }
    
    // Save credentials to localStorage
    saveCredentials() {
        try {
            const credentials = {
                authToken: this.authToken,
                sessionId: this.sessionId,
                consumerKey: this.consumerKey,
                isAuthenticated: this.isAuthenticated
            };
            localStorage.setItem(
                CONFIG.STORAGE.STORAGE_PREFIX + CONFIG.STORAGE.KEYS.USER_CREDENTIALS,
                JSON.stringify(credentials)
            );
        } catch (error) {
            console.error('Error saving credentials:', error);
        }
    }
    
    // Update request headers with authentication info
    updateHeaders() {
        this.headers.Authorization = this.authToken;
        this.headers.consumerKey = this.consumerKey;
        this.headers.sid = this.sessionId;
        this.headers.Auth = this.authToken;
    }
    
    // Generic API request method with error handling and retry logic
    async makeRequest(endpoint, method = 'GET', data = null, customHeaders = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const headers = { ...this.headers, ...customHeaders };
        
        const requestOptions = {
            method,
            headers,
            mode: 'cors',
            credentials: 'include'
        };
        
        if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
            requestOptions.body = JSON.stringify(data);
        }
        
        try {
            Logger.log('API Request', { url, method, data });
            
            const response = await fetch(url, requestOptions);
            const responseData = await response.json();
            
            if (!response.ok) {
                throw new Error(`API Error: ${response.status} - ${responseData.message || 'Unknown error'}`);
            }
            
            Logger.log('API Response', responseData);
            this.retryCount = 0; // Reset retry count on success
            
            return responseData;
        } catch (error) {
            Logger.error('API Request Failed', error);
            
            // Retry logic for network errors
            if (this.retryCount < this.maxRetries && this.isRetryableError(error)) {
                this.retryCount++;
                Logger.log(`Retrying request (${this.retryCount}/${this.maxRetries})`);
                
                await this.delay(CONFIG.ERROR_HANDLING.RETRY_DELAY * this.retryCount);
                return this.makeRequest(endpoint, method, data, customHeaders);
            }
            
            throw error;
        }
    }
    
    // Check if error is retryable
    isRetryableError(error) {
        const retryableErrors = ['NetworkError', 'TimeoutError', 'ConnectionError'];
        return retryableErrors.some(errorType => error.message.includes(errorType));
    }
    
    // Delay utility for retry logic
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // Authentication Methods
    async login(mobileNumber, password, consumerKey, consumerSecret) {
        try {
            const loginData = {
                mobileNumber,
                password,
                consumerKey,
                consumerSecret
            };
            
            const response = await this.makeRequest(CONFIG.API.ENDPOINTS.LOGIN, 'POST', loginData);
            
            if (response.stat === 'Ok') {
                this.authToken = response.data.token;
                this.sessionId = response.data.sid;
                this.consumerKey = consumerKey;
                this.isAuthenticated = true;
                
                this.updateHeaders();
                this.saveCredentials();
                
                Logger.log('Login successful');
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Login failed');
            }
        } catch (error) {
            Logger.error('Login failed', error);
            return { success: false, error: error.message };
        }
    }
    
    async logout() {
        try {
            if (this.isAuthenticated) {
                await this.makeRequest(CONFIG.API.ENDPOINTS.LOGOUT, 'POST');
            }
            
            this.authToken = null;
            this.sessionId = null;
            this.consumerKey = null;
            this.isAuthenticated = false;
            
            // Clear stored credentials
            localStorage.removeItem(CONFIG.STORAGE.STORAGE_PREFIX + CONFIG.STORAGE.KEYS.USER_CREDENTIALS);
            
            Logger.log('Logout successful');
            return { success: true };
        } catch (error) {
            Logger.error('Logout failed', error);
            return { success: false, error: error.message };
        }
    }
    
    // Order Management Methods
    async placeOrder(orderData) {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(CONFIG.API.ENDPOINTS.PLACE_ORDER, 'POST', orderData);
            
            if (response.stat === 'Ok') {
                Logger.log('Order placed successfully', response.data);
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Order placement failed');
            }
        } catch (error) {
            Logger.error('Order placement failed', error);
            return { success: false, error: error.message };
        }
    }
    
    async modifyOrder(orderId, modifyData) {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(
                `${CONFIG.API.ENDPOINTS.MODIFY_ORDER}/${orderId}`,
                'PUT',
                modifyData
            );
            
            if (response.stat === 'Ok') {
                Logger.log('Order modified successfully', response.data);
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Order modification failed');
            }
        } catch (error) {
            Logger.error('Order modification failed', error);
            return { success: false, error: error.message };
        }
    }
    
    async cancelOrder(orderId) {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(
                `${CONFIG.API.ENDPOINTS.CANCEL_ORDER}/${orderId}`,
                'DELETE'
            );
            
            if (response.stat === 'Ok') {
                Logger.log('Order cancelled successfully', response.data);
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Order cancellation failed');
            }
        } catch (error) {
            Logger.error('Order cancellation failed', error);
            return { success: false, error: error.message };
        }
    }
    
    // Data Retrieval Methods
    async getOrderBook() {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(CONFIG.API.ENDPOINTS.ORDER_BOOK);
            
            if (response.stat === 'Ok') {
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Failed to fetch order book');
            }
        } catch (error) {
            Logger.error('Failed to fetch order book', error);
            return { success: false, error: error.message };
        }
    }
    
    async getPositions() {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(CONFIG.API.ENDPOINTS.POSITIONS);
            
            if (response.stat === 'Ok') {
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Failed to fetch positions');
            }
        } catch (error) {
            Logger.error('Failed to fetch positions', error);
            return { success: false, error: error.message };
        }
    }
    
    async getHoldings() {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(CONFIG.API.ENDPOINTS.HOLDINGS);
            
            if (response.stat === 'Ok') {
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Failed to fetch holdings');
            }
        } catch (error) {
            Logger.error('Failed to fetch holdings', error);
            return { success: false, error: error.message };
        }
    }
    
    async getLimits() {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(CONFIG.API.ENDPOINTS.LIMITS);
            
            if (response.stat === 'Ok') {
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Failed to fetch limits');
            }
        } catch (error) {
            Logger.error('Failed to fetch limits', error);
            return { success: false, error: error.message };
        }
    }
    
    async getQuote(symbol, exchange) {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(
                `${CONFIG.API.ENDPOINTS.QUOTE}?symbol=${symbol}&exchange=${exchange}`
            );
            
            if (response.stat === 'Ok') {
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Failed to fetch quote');
            }
        } catch (error) {
            Logger.error('Failed to fetch quote', error);
            return { success: false, error: error.message };
        }
    }
    
    async searchScrip(searchText, exchange) {
        try {
            if (!this.isAuthenticated) {
                throw new Error('Not authenticated');
            }
            
            const response = await this.makeRequest(
                `${CONFIG.API.ENDPOINTS.SEARCH_SCRIP}?stext=${searchText}&exch=${exchange}`
            );
            
            if (response.stat === 'Ok') {
                return { success: true, data: response.data };
            } else {
                throw new Error(response.emsg || 'Failed to search scrip');
            }
        } catch (error) {
            Logger.error('Failed to search scrip', error);
            return { success: false, error: error.message };
        }
    }
}

}

// Logger utility class
class Logger {
    static log(message, data = null) {
        if (CONFIG.LOGGING.CONSOLE_LOGGING) {
            console.log(`[${new Date().toISOString()}] ${message}`, data || '');
        }
    }

    static error(message, error = null) {
        if (CONFIG.LOGGING.CONSOLE_LOGGING) {
            console.error(`[${new Date().toISOString()}] ERROR: ${message}`, error || '');
        }
    }

    static warn(message, data = null) {
        if (CONFIG.LOGGING.CONSOLE_LOGGING) {
            console.warn(`[${new Date().toISOString()}] WARNING: ${message}`, data || '');
        }
    }
}

// Create global API instance
const kotakAPI = new KotakNeoAPI();
