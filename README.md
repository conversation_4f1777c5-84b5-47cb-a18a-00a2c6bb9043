# Kapil Trading Bot - Automated Trading Platform

A comprehensive automated trading bot for Indian stock markets using Kotak Neo APIs. This application supports equity, options, and commodity trading with advanced features like stop-loss, target management, and scheduled trading.

## 🚀 Features

### Core Trading Features
- **Multi-Asset Trading**: Support for Equity (NSE/BSE), Options (NSE), and Commodities (MCX)
- **Real-time Market Data**: Live tick-by-tick price updates via WebSocket
- **Order Management**: Place, modify, and cancel orders with full order book tracking
- **Stop Loss & Target**: Automatic stop-loss and target price monitoring and execution
- **Scheduled Trading**: Schedule trades for specific times with automated execution
- **Risk Management**: Built-in risk controls and position size limits

### Advanced Features
- **Automated Execution**: Continuous monitoring and automatic order execution
- **Real-time Notifications**: Browser notifications and sound alerts
- **Position Tracking**: Live P&L calculation and position monitoring
- **Order History**: Complete order and trade history tracking
- **Market Data Subscriptions**: Subscribe to multiple symbols simultaneously

### User Interface
- **Responsive Design**: Works on desktop and mobile devices
- **Real-time Dashboard**: Live market data, orders, and positions
- **Easy Configuration**: Simple API setup and symbol management
- **Visual Indicators**: Color-coded price changes and status indicators

## 📋 Prerequisites

1. **Kotak Neo Trading Account**: You need an active Kotak Neo trading account
2. **API Credentials**: Consumer Key and Consumer Secret from Kotak Neo
3. **Modern Web Browser**: Chrome, Firefox, Safari, or Edge
4. **Internet Connection**: Stable internet for real-time data

## 🛠️ Installation & Setup

### Step 1: Download the Application
1. Download all files to a folder on your computer
2. Ensure all files are in the same directory structure:
   ```
   kapil-trading-bot/
   ├── index.html
   ├── styles.css
   ├── README.md
   ├── Tutorial.html
   └── js/
       ├── config.js
       ├── api.js
       ├── websocket.js
       ├── trading.js
       ├── scheduler.js
       └── app.js
   ```

### Step 2: Get API Credentials
1. Log in to your Kotak Neo account
2. Go to API section and generate:
   - Consumer Key
   - Consumer Secret
3. Note down your mobile number and password

### Step 3: Launch the Application
1. Open `index.html` in your web browser
2. The application will load automatically
3. No installation or server setup required!

## 🔧 Configuration

### API Configuration
1. Enter your Kotak Neo credentials in the API Configuration section:
   - Consumer Key
   - Consumer Secret
   - Mobile Number
   - Password
2. Click "Login" to authenticate

### Risk Management Settings
Edit `js/config.js` to modify risk parameters:
```javascript
RISK_MANAGEMENT: {
    MAX_DAILY_LOSS: 50000,        // Maximum daily loss limit
    MAX_POSITION_SIZE: 100000,    // Maximum position size
    STOP_LOSS_PERCENTAGE: 2,      // Default stop loss %
    TARGET_PERCENTAGE: 4,         // Default target %
    MAX_ORDERS_PER_MINUTE: 20,    // Rate limiting
    MAX_ORDERS_PER_DAY: 200       // Daily order limit
}
```

## 📖 How to Use

### 1. Login and Connect
1. Fill in your API credentials
2. Click "Login" to authenticate
3. Wait for "Connected" status
4. WebSocket connections will establish automatically

### 2. Subscribe to Market Data
1. Select exchange (NSE/BSE/MCX)
2. Enter symbol (e.g., RELIANCE, NIFTY)
3. Choose instrument type
4. Click "Subscribe to Feed"
5. Live prices will appear in the Market Data section

### 3. Place Orders
1. Click "BUY" or "SELL"
2. Enter symbol, quantity, and price
3. Set stop loss and target prices (optional)
4. Click "Place Order"
5. Order will appear in Active Orders section

### 4. Schedule Trades
1. Set the execution time
2. Choose action (Buy/Sell)
3. Enter symbol and quantity
4. Click "Schedule Trade"
5. Trade will execute automatically at the specified time

### 5. Monitor Performance
- View active orders in real-time
- Track positions and P&L
- Monitor scheduled trades
- Check daily statistics

## 🎯 Trading Strategies

### Basic Buy/Sell
```javascript
// Example: Buy 100 shares of RELIANCE at market price
{
    symbol: "RELIANCE",
    exchange: "NSE",
    transactionType: "B",
    quantity: 100,
    orderType: "MKT"
}
```

### Stop Loss Trading
```javascript
// Example: Buy with 2% stop loss and 4% target
{
    symbol: "RELIANCE",
    exchange: "NSE",
    transactionType: "B",
    quantity: 100,
    orderType: "LMT",
    price: 2500,
    stopLoss: 2450,    // 2% below entry
    target: 2600       // 4% above entry
}
```

### Scheduled Trading
```javascript
// Example: Schedule a buy order for market opening
{
    symbol: "NIFTY",
    exchange: "NSE",
    transactionType: "B",
    quantity: 50,
    scheduledTime: "2024-01-15T09:15:00",
    orderType: "MKT"
}
```

## ⚠️ Risk Management

### Built-in Safety Features
- **Daily Loss Limits**: Automatically stops trading if daily loss exceeds limit
- **Position Size Limits**: Prevents oversized positions
- **Order Rate Limiting**: Prevents excessive order placement
- **Stop Loss Monitoring**: Continuous price monitoring for stop loss execution
- **Circuit Breakers**: Automatic halt on repeated failures

### Best Practices
1. **Start Small**: Begin with small quantities to test the system
2. **Set Stop Losses**: Always use stop losses to limit downside risk
3. **Monitor Regularly**: Keep an eye on positions and market conditions
4. **Test Thoroughly**: Use paper trading or small amounts initially
5. **Stay Updated**: Keep track of market news and events

## 🔍 Troubleshooting

### Common Issues

#### Login Failed
- **Check Credentials**: Verify Consumer Key, Secret, Mobile, and Password
- **Network Issues**: Ensure stable internet connection
- **API Limits**: Check if API access is enabled in your Kotak account

#### WebSocket Connection Failed
- **Firewall**: Check if WebSocket connections are blocked
- **Browser**: Try a different browser or disable extensions
- **Network**: Ensure stable internet connection

#### Orders Not Executing
- **Market Hours**: Ensure market is open for trading
- **Insufficient Funds**: Check available margin/balance
- **Symbol Format**: Verify correct symbol format for the exchange
- **Risk Limits**: Check if risk management limits are exceeded

#### Market Data Not Updating
- **Subscription**: Ensure symbol is properly subscribed
- **WebSocket**: Check WebSocket connection status
- **Symbol**: Verify symbol exists on the selected exchange

### Error Codes
- **401**: Authentication failed - check credentials
- **403**: Access denied - check API permissions
- **429**: Rate limit exceeded - reduce order frequency
- **500**: Server error - try again later

## 📊 Performance Monitoring

### Key Metrics
- **Orders Today**: Total orders placed
- **Execution Rate**: Percentage of successful orders
- **P&L**: Daily profit/loss
- **Position Count**: Number of open positions
- **Success Rate**: Percentage of profitable trades

### Logs and Debugging
- Check browser console for detailed logs
- All API calls and responses are logged
- WebSocket messages are tracked
- Error details are captured for debugging

## 🔒 Security

### Data Protection
- **Local Storage**: Credentials stored locally in browser
- **No Server**: No data sent to external servers
- **Encryption**: API communications are encrypted
- **Session Management**: Automatic session handling

### Best Practices
1. **Secure Environment**: Use the application on a secure computer
2. **Regular Updates**: Keep browser updated
3. **Logout**: Always logout when done trading
4. **Monitor Access**: Check for unauthorized access regularly

## 📞 Support

### Getting Help
1. **Documentation**: Read this README thoroughly
2. **Tutorial**: Check Tutorial.html for API documentation
3. **Logs**: Check browser console for error details
4. **Community**: Join trading communities for discussions

### Contact Information
- **Kotak Neo API Support**: <EMAIL>
- **Technical Issues**: Check browser console and logs

## 📄 License

This project is for educational and personal use only. Please ensure compliance with:
- Kotak Neo API terms of service
- SEBI regulations for algorithmic trading
- Local trading laws and regulations

## ⚖️ Disclaimer

**IMPORTANT**: This software is provided "as is" without warranty. Trading involves substantial risk of loss. The developers are not responsible for any financial losses incurred through the use of this software. Always:

- Test thoroughly before live trading
- Start with small amounts
- Understand the risks involved
- Comply with all applicable regulations
- Seek professional advice if needed

## 🔄 Updates and Maintenance

### Regular Maintenance
- Monitor API changes from Kotak Neo
- Update browser regularly
- Check for new features and improvements
- Backup trading data regularly

### Version History
- **v1.0.0**: Initial release with core trading features
- **v1.1.0**: Added scheduled trading and enhanced UI
- **v1.2.0**: Improved risk management and error handling

---

**Happy Trading! 📈**

Remember: The key to successful automated trading is proper risk management, thorough testing, and continuous monitoring. Start small, learn the system, and gradually increase your trading size as you gain confidence.
