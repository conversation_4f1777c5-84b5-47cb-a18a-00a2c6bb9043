const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 8000;

// Enable CORS for all routes
app.use(cors({
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'neo-fin-key', 'sid', 'Auth', 'consumerKey', 'accept'],
    credentials: true
}));

// Serve static files from current directory
app.use(express.static(path.join(__dirname)));

// Handle SPA routing - serve index.html for all routes
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Start server
app.listen(PORT, () => {
    console.log('🚀 Kotak Neo Trading Bot Server Started!');
    console.log('');
    console.log('📊 Server Details:');
    console.log(`   Port: ${PORT}`);
    console.log(`   URL: http://localhost:${PORT}`);
    console.log('');
    console.log('🔗 Access your trading bot at:');
    console.log(`   👉 http://localhost:${PORT}`);
    console.log('');
    console.log('🛠️  Features enabled:');
    console.log('   ✅ CORS enabled for API requests');
    console.log('   ✅ Static file serving');
    console.log('   ✅ Kotak Neo API integration');
    console.log('   ✅ TOTP authentication support');
    console.log('');
    console.log('📝 To stop the server: Press Ctrl+C');
    console.log('');
    
    // Try to open browser automatically (optional)
    const open = require('child_process').exec;
    setTimeout(() => {
        console.log('🌐 Opening browser...');
        const url = `http://localhost:${PORT}`;
        
        // Cross-platform browser opening
        const start = process.platform === 'darwin' ? 'open' : 
                     process.platform === 'win32' ? 'start' : 'xdg-open';
        
        open(`${start} ${url}`, (error) => {
            if (error) {
                console.log('💡 Please manually open your browser and go to:', url);
            }
        });
    }, 1000);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    console.log('👋 Trading Bot Server stopped.');
    process.exit(0);
});
