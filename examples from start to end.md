1. Acess token
POST
Access Token
https://napi.kotaksecurities.com/oauth2/token
Generated from cURL: curl -k -X POST https://napi.kotaksecurities.com/oauth2/token -d "grant_type=client_credentials" -H "Authorization: Basic MW1La1VYcWxIdTZlNEw0NDR6RmluRzdjNnRzYTpENGV1bEZZOXc3X0ZBRWRTWW5vRWY4blI2emth"

HEADERS
Authorization
Basic MW1La1VYcWxIdTZlNEw0NDR6RmluRzdjNnRzYTpENGV1bEZZOXc3X0ZBRWRTWW5vRWY4blI2emth

Body
urlencoded
grant_type
client_credentials


var settings = {
  "url": "https://napi.kotaksecurities.com/oauth2/token",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Authorization": "Basic MW1La1VYcWxIdTZlNEw0NDR6RmluRzdjNnRzYTpENGV1bEZZOXc3X0ZBRWRTWW5vRWY4blI2emth"
  },
  "data": {
    "grant_type": "client_credentials"
  }
};

$.ajax(settings).done(function (response) {
  console.log(response);
});

{
  "access_token": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
  "scope": "default",
  "token_type": "Bearer",
  "expires_in": *********
}

2. Login with TOTP
POST
Step 1 - Get View Token
https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/login
Generated from cURL: curl --location 'https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/login'
--header 'Authorization: Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
--header 'neo-fin-key: neotradeapi'
--header 'Content-Type: application/json'
--data '{
"mobileNumber": "+9170**3",
"ucc": "Y*0",
"totp": "009405"
}'

HEADERS
Authorization
Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

neo-fin-key
neotradeapi

Content-Type
application/json

Body
raw (json)
json
{
    "mobileNumber": "+917********3",
    "ucc": "Y***0",
    "totp": "894913"
}
Example Request
Step 1 - Get View Token
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/login",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Authorization": "Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "neo-fin-key": "neotradeapi",
    "Content-Type": "application/json"
  },
  "data": JSON.stringify({
    "mobileNumber": "+9170******93",
    "ucc": "******",
    "totp": "******"
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
200 OK
Example Response
Body
Headers (27)
View More
json
{
  "data": {
    "token": "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "sid": "b979ea59-9e86-4c50-83b0-099ea7c11b07",
    "rid": "9e13da1c-5186-4bd0-b0dc-6ab819f71e18",
    "hsServerId": "",
    "isUserPwdExpired": false,
    "ucc": "YOJE0",
    "greetingName": "PRIYANKA",
    "isTrialAccount": false,
    "dataCenter": "E21",
    "searchAPIKey": "",
    "derivativesRiskDisclosure": "Risk Disclosure on Derivatives\n\nAs per a SEBI study dated 25 Jan 2023- \n• 9 out of 10 individual traders in equity Futures and Options Segment, incurred net losses.\n• On an average, loss makers registered net trading loss close to Rs.50,000.\n• Over and above the net trading losses incurred, loss makers expended an additional 28% of net trading losses as transaction costs.\n• Those making net trading profits, incurred between 15% to 50% of such profits as transaction cost.\n\nFor more information please check out : https://www.sebi.gov.in/reports-and-statistics/research/jan-2023/study-analysis-of-profit-and-loss-of-individual-traders-dealing-in-equity-fando-segment_67525.html",
    "mfAccess": 1,
    "dataCenterMap": null,
    "dormancyStatus": "A",
    "asbaStatus": "",
    "clientType": "RI",
    "isNRI": false,
    "kId": "AVRPC7535J",
    "kType": "View",
    "status": "success",
    "incRange": 0,
    "incUpdFlag": "",
    "clientGroup": ""
  }
}
POST
Step 2 - Get Final Session Token
https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/validate
Generated from cURL: curl -X 'POST'
'https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/validate'
-H 'accept: application/json'
-H 'sid: x'
-H 'Auth: x'
-H 'neo-fin-key: x'
-H 'Content-Type: application/json'
-H 'Authorization: Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
-d '{
"mpin": "**"
}'

HEADERS
accept
application/json

sid
b979ea59-9e86-4c50-83b0-099ea7c11b07

Auth
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

neo-fin-key
neotradeapi

Content-Type
application/json

Authorization
Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

Body
raw (json)
json
{
  "mpin": "******"
}
Example Request
Step 2 - Get Final Session Token
View More
javascript
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/validate",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "sid": "b979ea59-9e86-4c50-83b0-099ea7c11b07",
    "Auth": "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "neo-fin-key": "neotradeapi",
    "Content-Type": "application/json",
    "Authorization": "Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
  "data": JSON.stringify({
    "mpin": "******"
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
200 OK
Example Response
Body
Headers (27)
View More
json
{
  "data": {
    "token": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "sid": "8a43c2a9-004f-4186-9d9e-824c07014f60",
    "rid": "9e13da1c-5186-4bd0-b0dc-6ab819f71e18",
    "hsServerId": "",
    "isUserPwdExpired": false,
    "ucc": "YOJE0",
    "greetingName": "PRIYANKA",
    "isTrialAccount": false,
    "dataCenter": "E21",
    "searchAPIKey": "",
    "derivativesRiskDisclosure": "Risk Disclosure on Derivatives\n\nAs per a SEBI study dated 25 Jan 2023- \n• 9 out of 10 individual traders in equity Futures and Options Segment, incurred net losses.\n• On an average, loss makers registered net trading loss close to Rs.50,000.\n• Over and above the net trading losses incurred, loss makers expended an additional 28% of net trading losses as transaction costs.\n• Those making net trading profits, incurred between 15% to 50% of such profits as transaction cost.\n\nFor more information please check out : https://www.sebi.gov.in/reports-and-statistics/research/jan-2023/study-analysis-of-profit-and-loss-of-individual-traders-dealing-in-equity-fando-segment_67525.html",
    "mfAccess": 1,
    "dataCenterMap": null,
    "dormancyStatus": "A",
    "asbaStatus": "",
    "clientType": "RI",
    "isNRI": false,
    "kId": "AVRPC7535J",
    "kType": "Trade",
    "status": "success",
    "incRange": 0,
    "incUpdFlag": "",
    "clientGroup": ""
  }
}
 Examples 
 var settings = {
  "url": "https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/login",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "Authorization": "Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "neo-fin-key": "neotradeapi",
    "Content-Type": "application/json"
  },
  "data": JSON.stringify({
    "mobileNumber": "+9170******93",
    "ucc": "******",
    "totp": "******"
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});
{
  "data": {
    "token": "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "sid": "b979ea59-9e86-4c50-83b0-099ea7c11b07",
    "rid": "9e13da1c-5186-4bd0-b0dc-6ab819f71e18",
    "hsServerId": "",
    "isUserPwdExpired": false,
    "ucc": "YOJE0",
    "greetingName": "PRIYANKA",
    "isTrialAccount": false,
    "dataCenter": "E21",
    "searchAPIKey": "",
    "derivativesRiskDisclosure": "Risk Disclosure on Derivatives\n\nAs per a SEBI study dated 25 Jan 2023- \n• 9 out of 10 individual traders in equity Futures and Options Segment, incurred net losses.\n• On an average, loss makers registered net trading loss close to Rs.50,000.\n• Over and above the net trading losses incurred, loss makers expended an additional 28% of net trading losses as transaction costs.\n• Those making net trading profits, incurred between 15% to 50% of such profits as transaction cost.\n\nFor more information please check out : https://www.sebi.gov.in/reports-and-statistics/research/jan-2023/study-analysis-of-profit-and-loss-of-individual-traders-dealing-in-equity-fando-segment_67525.html",
    "mfAccess": 1,
    "dataCenterMap": null,
    "dormancyStatus": "A",
    "asbaStatus": "",
    "clientType": "RI",
    "isNRI": false,
    "kId": "AVRPC7535J",
    "kType": "View",
    "status": "success",
    "incRange": 0,
    "incUpdFlag": "",
    "clientGroup": ""
  }
}

var settings = {
  "url": "https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/validate",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "sid": "b979ea59-9e86-4c50-83b0-099ea7c11b07",
    "Auth": "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "neo-fin-key": "neotradeapi",
    "Content-Type": "application/json",
    "Authorization": "Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
  "data": JSON.stringify({
    "mpin": "******"
  }),
};

$.ajax(settings).done(function (response) {
  console.log(response);
});

{
  "data": {
    "token": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "sid": "8a43c2a9-004f-4186-9d9e-824c07014f60",
    "rid": "9e13da1c-5186-4bd0-b0dc-6ab819f71e18",
    "hsServerId": "",
    "isUserPwdExpired": false,
    "ucc": "YOJE0",
    "greetingName": "PRIYANKA",
    "isTrialAccount": false,
    "dataCenter": "E21",
    "searchAPIKey": "",
    "derivativesRiskDisclosure": "Risk Disclosure on Derivatives\n\nAs per a SEBI study dated 25 Jan 2023- \n• 9 out of 10 individual traders in equity Futures and Options Segment, incurred net losses.\n• On an average, loss makers registered net trading loss close to Rs.50,000.\n• Over and above the net trading losses incurred, loss makers expended an additional 28% of net trading losses as transaction costs.\n• Those making net trading profits, incurred between 15% to 50% of such profits as transaction cost.\n\nFor more information please check out : https://www.sebi.gov.in/reports-and-statistics/research/jan-2023/study-analysis-of-profit-and-loss-of-individual-traders-dealing-in-equity-fando-segment_67525.html",
    "mfAccess": 1,
    "dataCenterMap": null,
    "dormancyStatus": "A",
    "asbaStatus": "",
    "clientType": "RI",
    "isNRI": false,
    "kId": "AVRPC7535J",
    "kType": "Trade",
    "status": "success",
    "incRange": 0,
    "incUpdFlag": "",
    "clientGroup": ""
  }
}


Scrip Master
GET
Filename
https://gw-napi.kotaksecurities.com/Files/1.0/masterscrip/v2/file-paths
For order placement, you should use the value in pTrdSymbol column to pass in the "ts" parameter.

Use the dStrikePrice and divide the vlaue by 100 to get the exact strike price.

Note: 'lExpiryDate' is the column you should refer for expiry date. For getting the readable expiry date
1. nse_fo and cde_fo: Add ********* to the epoch value and convert it to IST.

2. mcx_fo and bse_fo: Epoch (lExpiryDate) can be directly converted into human readable date.

For Indices the tokens are their names. Use the below names in place of token for indices.


Exchange Identifier(Token)	Exchange Segment
Nifty 50	nse_cm
Nifty Bank	nse_cm
Nifty Fin Service	nse_cm
SENSEX	nse_cm
INDIA VIX	nse_cm
NIFTY MIDCAP 100	nse_cm
Nifty 100	nse_cm
Nifty PSU Bank	nse_cm
Nifty Pharma	nse_cm
Nifty IT	nse_cm
Nifty PSE	nse_cm
Nifty FMCG	nse_cm
Nifty 500	nse_cm
Nifty Auto	nse_cm
Nifty CPSE	nse_cm
Nifty 200	nse_cm
Nifty Next 50	nse_cm
NIFTY MID SELECT	nse_cm
SENSEX	bse_cm
BANKEX	bse_cm
SNSX50	bse_cm

HEADERS
accept
*/*

Authorization
Bearer eyJ4NXQiOiJNbUprWWpVMlpETmpNelpqTURBM05UZ3pObUUxTm1NNU1qTXpNR1kyWm1OaFpHUTFNakE1TmciLCJraWQiOiJaalJqTUdRek9URmhPV1EwTm1WallXWTNZemRtWkdOa1pUUmpaVEUxTlRnMFkyWTBZVEUyTlRCaVlURTRNak5tWkRVeE5qZ3pPVGM0TWpGbFkyWXpOUV9SUzI1NiIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PNlbb_IScbCwsx_xjSg5elL0kHoScfTpljTpsNEVjLwCSAIXaYmI8mfyOnjXFC8weYYeJvWFU95pT5ehBth9mBGfvRptJbUdPl8NnZQsGHFhkgg2QkNU4n12psRA8Q5LN38pPlZHaCOmNb_OofGTDQ5hwjFoXY5wxLVfB05fN4FIUC-ZDTbqq0Mp7RPAxmLOuNpb1G-w8DQnXLmz_RwpYyCr18vP35yQMpIQzOfvgdWbBvJW_xBDJ9FpdFTjIMvqknGlco8hdXRCpTX-GRGvMh1CVYJdu7JVyp2mpoaxKrASJcAmRnuAdeS6UEVIaAb3rEd-koHjqmp3OQelyf2MJg


request 
var settings = {
  "url": "https://gw-napi.kotaksecurities.com/Files/1.0/masterscrip/v2/file-paths",
  "method": "GET",
  "timeout": 0,
  "headers": {
    "accept": "*/*",
    "Authorization": "Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
};

$.ajax(settings).done(function (response) {
  console.log(response);
});

response 
{
  "data": {
    "filesPaths": [
      "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod/2025-03-19/transformed/cde_fo.csv",
      "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod/2025-03-19/transformed/mcx_fo.csv",
      "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod/2025-03-19/transformed/nse_fo.csv",
      "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod/2025-03-19/transformed/bse_fo.csv",
      "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod/2025-03-19/transformed-v1/nse_cm-v1.csv",
      "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod/2025-03-19/transformed-v1/bse_cm-v1.csv"
    ],
    "baseFolder": "https://lapi.kotaksecurities.com/wso2-scripmaster/v1/prod"
  }
}



Table
Field	Type	Description
am	String	AMO
cf	String	Customer firm
dq	String	Disclosed quantity
es	String	Exchange segment
mp	String	Market Protection
pc	String	Product code
pf	String	PosSqrFlg
pr	String	Price
pt	String	Order type
qt	String	Quantity
HEADERS
accept
application/json

Sid
8a43c2a9-004f-4186-9d9e-824c07014f60

Auth
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

neo-fin-key
neotradeapi

Content-Type
application/x-www-form-urlencoded

Authorization
Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

Body
urlencoded
jData
{"am":"NO", "dq":"0","es":"nse_cm", "mp":"0", "pc":"CNC", "pf":"N", "pr":"6.50", "pt":"L", "qt":"1", "rt":"DAY", "tp":"0", "ts":"IDEA-EQ", "tt":"B"}

example var settings = {
  "url": "https://gw-napi.kotaksecurities.com/Orders/2.0/quick/order/rule/ms/place",
  "method": "POST",
  "timeout": 0,
  "headers": {
    "accept": "application/json",
    "Sid": "8a43c2a9-004f-4186-9d9e-824c07014f60",
    "Auth": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "neo-fin-key": "neotradeapi",
    "Content-Type": "application/x-www-form-urlencoded",
    "Authorization": "Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
  },
  "data": {
    "jData": "{\"am\":\"NO\", \"dq\":\"0\",\"es\":\"nse_cm\", \"mp\":\"0\", \"pc\":\"CNC\", \"pf\":\"N\", \"pr\":\"6.50\", \"pt\":\"L\", \"qt\":\"1\", \"rt\":\"DAY\", \"tp\":\"0\", \"ts\":\"IDEA-EQ\", \"tt\":\"B\"}"
  }
};

$.ajax(settings).done(function (response) {
  console.log(response);
});



