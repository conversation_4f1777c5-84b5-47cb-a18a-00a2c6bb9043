{"name": "kotak-neo-trading-bot", "version": "1.0.0", "description": "Automated trading bot for Kotak Neo API with TOTP authentication", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "serve": "http-server -p 8000 --cors -o", "install-server": "npm install -g http-server"}, "keywords": ["trading", "kotak-neo", "api", "automation", "totp"], "author": "Trading Bot Developer", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"http-server": "^14.1.1"}}