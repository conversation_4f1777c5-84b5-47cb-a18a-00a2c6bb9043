<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Bot Demo - Features Overview</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .demo-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 20px auto;
            max-width: 1200px;
            overflow: hidden;
        }
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .feature-card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        .demo-section {
            padding: 30px;
        }
        .btn-demo {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-demo:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        .stats-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }
        .code-block {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
        }
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #007bff;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 30px;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -23px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #007bff;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1><i class="fas fa-robot"></i> Kapil Trading Bot</h1>
            <h3>Automated Trading Platform Demo</h3>
            <p class="lead">Experience the power of automated trading with real-time market data, intelligent order management, and advanced risk controls.</p>
            <a href="index.html" class="btn btn-demo btn-lg mt-3">
                <i class="fas fa-rocket"></i> Launch Trading Bot
            </a>
        </div>

        <div class="demo-section">
            <div class="row">
                <div class="col-md-8">
                    <h2><i class="fas fa-star text-warning"></i> Key Features</h2>
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card feature-card h-100">
                                <div class="card-body text-center">
                                    <div class="feature-icon text-primary">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <h5>Real-time Market Data</h5>
                                    <p>Live tick-by-tick price updates via WebSocket connections for instant market insights.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card feature-card h-100">
                                <div class="card-body text-center">
                                    <div class="feature-icon text-success">
                                        <i class="fas fa-shopping-cart"></i>
                                    </div>
                                    <h5>Smart Order Management</h5>
                                    <p>Place, modify, and cancel orders with automatic stop-loss and target execution.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card feature-card h-100">
                                <div class="card-body text-center">
                                    <div class="feature-icon text-warning">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <h5>Scheduled Trading</h5>
                                    <p>Schedule trades for specific times with automated execution and monitoring.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card feature-card h-100">
                                <div class="card-body text-center">
                                    <div class="feature-icon text-danger">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <h5>Risk Management</h5>
                                    <p>Built-in risk controls with daily loss limits and position size management.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <h4><i class="fas fa-trophy"></i> Platform Stats</h4>
                        <div class="row mt-4">
                            <div class="col-6">
                                <h3>3</h3>
                                <small>Exchanges Supported</small>
                            </div>
                            <div class="col-6">
                                <h3>4</h3>
                                <small>Asset Classes</small>
                            </div>
                            <div class="col-6 mt-3">
                                <h3>24/7</h3>
                                <small>Monitoring</small>
                            </div>
                            <div class="col-6 mt-3">
                                <h3>∞</h3>
                                <small>Symbols Supported</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mt-4">
                        <div class="card-header bg-primary text-white">
                            <h6><i class="fas fa-info-circle"></i> Quick Start</h6>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                <div class="timeline-item">
                                    <strong>Step 1:</strong> Get Kotak Neo API credentials
                                </div>
                                <div class="timeline-item">
                                    <strong>Step 2:</strong> Launch the trading bot
                                </div>
                                <div class="timeline-item">
                                    <strong>Step 3:</strong> Login with your credentials
                                </div>
                                <div class="timeline-item">
                                    <strong>Step 4:</strong> Start trading!
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section bg-light">
            <h2><i class="fas fa-code text-primary"></i> Sample Trading Strategies</h2>
            <div class="row">
                <div class="col-md-6">
                    <h5>Basic Buy Order with Stop Loss</h5>
                    <div class="code-block">
{<br>
&nbsp;&nbsp;symbol: "RELIANCE",<br>
&nbsp;&nbsp;exchange: "NSE",<br>
&nbsp;&nbsp;action: "BUY",<br>
&nbsp;&nbsp;quantity: 100,<br>
&nbsp;&nbsp;price: 2500,<br>
&nbsp;&nbsp;stopLoss: 2375,<br>
&nbsp;&nbsp;target: 2750<br>
}
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>Scheduled Market Order</h5>
                    <div class="code-block">
{<br>
&nbsp;&nbsp;symbol: "NIFTY",<br>
&nbsp;&nbsp;exchange: "NSE",<br>
&nbsp;&nbsp;action: "BUY",<br>
&nbsp;&nbsp;quantity: 50,<br>
&nbsp;&nbsp;orderType: "MARKET",<br>
&nbsp;&nbsp;scheduledTime: "09:15:00"<br>
}
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2><i class="fas fa-exchange-alt text-success"></i> Supported Markets</h2>
            <div class="row">
                <div class="col-md-4 text-center">
                    <div class="card feature-card">
                        <div class="card-body">
                            <div class="feature-icon text-primary">
                                <i class="fas fa-building"></i>
                            </div>
                            <h5>NSE (National Stock Exchange)</h5>
                            <p>Equity and Options trading with real-time data and order execution.</p>
                            <ul class="list-unstyled">
                                <li>✓ Equity Cash</li>
                                <li>✓ Equity Futures</li>
                                <li>✓ Index Options</li>
                                <li>✓ Stock Options</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <div class="card feature-card">
                        <div class="card-body">
                            <div class="feature-icon text-warning">
                                <i class="fas fa-coins"></i>
                            </div>
                            <h5>MCX (Multi Commodity Exchange)</h5>
                            <p>Commodity trading including precious metals, energy, and agricultural products.</p>
                            <ul class="list-unstyled">
                                <li>✓ Gold & Silver</li>
                                <li>✓ Crude Oil</li>
                                <li>✓ Natural Gas</li>
                                <li>✓ Agricultural</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <div class="card feature-card">
                        <div class="card-body">
                            <div class="feature-icon text-info">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <h5>BSE (Bombay Stock Exchange)</h5>
                            <p>Additional equity trading options with comprehensive market coverage.</p>
                            <ul class="list-unstyled">
                                <li>✓ Equity Cash</li>
                                <li>✓ SME Stocks</li>
                                <li>✓ Mutual Funds</li>
                                <li>✓ Bonds & Debentures</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section bg-light">
            <h2><i class="fas fa-cogs text-danger"></i> Advanced Features</h2>
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-brain"></i> Intelligent Risk Management</h5>
                    <ul>
                        <li><strong>Daily Loss Limits:</strong> Automatic trading halt when daily loss exceeds preset limits</li>
                        <li><strong>Position Size Control:</strong> Prevents oversized positions that could impact portfolio</li>
                        <li><strong>Order Rate Limiting:</strong> Compliance with exchange regulations and error prevention</li>
                        <li><strong>Circuit Breakers:</strong> Automatic halt on repeated failures or unusual activity</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5><i class="fas fa-bell"></i> Real-time Monitoring & Alerts</h5>
                    <ul>
                        <li><strong>Price Alerts:</strong> Instant notifications when prices reach target levels</li>
                        <li><strong>Order Updates:</strong> Real-time order status updates via WebSocket</li>
                        <li><strong>P&L Tracking:</strong> Live profit/loss calculation and position monitoring</li>
                        <li><strong>Sound Notifications:</strong> Audio alerts for important trading events</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <div class="row">
                <div class="col-md-8">
                    <h2><i class="fas fa-question-circle text-info"></i> Frequently Asked Questions</h2>
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    Do I need to install any software?
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    No! The trading bot runs entirely in your web browser. Just open index.html and start trading.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    What do I need to get started?
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    You need a Kotak Neo trading account and API credentials (Consumer Key and Secret). The bot handles everything else automatically.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    Is my data secure?
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Yes! All data is stored locally in your browser. No information is sent to external servers except for official Kotak Neo API calls.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h6><i class="fas fa-download"></i> Get Started Now</h6>
                        </div>
                        <div class="card-body text-center">
                            <p>Ready to start automated trading?</p>
                            <a href="index.html" class="btn btn-demo btn-lg mb-3">
                                <i class="fas fa-play"></i> Launch Bot
                            </a>
                            <br>
                            <a href="test.html" class="btn btn-outline-primary">
                                <i class="fas fa-vial"></i> Run Tests
                            </a>
                            <br><br>
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Read the README.md and USER_GUIDE.md for detailed instructions.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section bg-dark text-white">
            <div class="text-center">
                <h3><i class="fas fa-exclamation-triangle text-warning"></i> Important Disclaimer</h3>
                <p class="lead">
                    Trading involves substantial risk of loss. This software is provided for educational purposes only. 
                    Always test with small amounts and understand the risks before live trading.
                </p>
                <div class="row mt-4">
                    <div class="col-md-4">
                        <h6><i class="fas fa-shield-alt"></i> Risk Management</h6>
                        <p>Built-in safety features and risk controls</p>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-graduation-cap"></i> Educational</h6>
                        <p>Learn automated trading concepts safely</p>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-users"></i> Community</h6>
                        <p>Join trading communities for support</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
