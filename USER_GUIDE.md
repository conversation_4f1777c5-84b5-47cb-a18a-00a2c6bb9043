# Kapil Trading Bot - Complete User Guide

## Table of Contents
1. [Getting Started](#getting-started)
2. [Dashboard Overview](#dashboard-overview)
3. [Trading Operations](#trading-operations)
4. [Advanced Features](#advanced-features)
5. [Risk Management](#risk-management)
6. [Troubleshooting](#troubleshooting)

## Getting Started

### First Time Setup

#### Step 1: Prepare Your Credentials
Before using the trading bot, gather the following from your Kotak Neo account:
- **Consumer Key**: Found in your API section
- **Consumer Secret**: Generated along with Consumer Key
- **Mobile Number**: Registered with your trading account
- **Password**: Your trading account password

#### Step 2: Launch the Application
1. Open `index.html` in your web browser
2. You'll see the main dashboard with three panels
3. The application works entirely in your browser - no installation needed

#### Step 3: Initial Login
1. In the "API Configuration" section (left panel):
   - Enter your Consumer Key
   - Enter your Consumer Secret
   - Enter your Mobile Number
   - Enter your Password
2. Click "Login"
3. Wait for the "Connected" status to appear

## Dashboard Overview

### Left Panel - Market Data & Controls

#### API Configuration Section
- **Purpose**: Authenticate with Kotak Neo APIs
- **Status Indicator**: Shows connection status (Connected/Disconnected)
- **Auto-Save**: Credentials are saved locally for convenience

#### Symbol Selection Section
- **Exchange Dropdown**: Choose NSE (Equity/Options), MCX (Commodity), or BSE
- **Symbol Input**: Enter trading symbol (e.g., RELIANCE, NIFTY, GOLD)
- **Instrument Type**: Select EQ (Equity), FUT (Futures), OPT (Options), COM (Commodity)
- **Subscribe Button**: Adds symbol to live market data feed

#### Live Market Data Section
- **Real-time Prices**: Shows current price, change, and percentage change
- **Color Coding**: Green (up), Red (down), Gray (no change)
- **Last Update Time**: Shows when price was last updated
- **Multiple Symbols**: Can track multiple symbols simultaneously

### Center Panel - Trading Interface

#### Order Placement Section
- **Buy/Sell Buttons**: Select transaction type (highlighted when selected)
- **Symbol Field**: Enter the symbol you want to trade
- **Quantity Field**: Number of shares/lots to trade
- **Price Field**: Leave 0 for market orders, enter price for limit orders
- **Order Type Dropdown**: Market, Limit, Stop Loss, Stop Loss Market
- **Stop Loss Price**: Automatic exit price if trade goes against you
- **Target Price**: Automatic exit price when profit target is reached

#### Scheduled Trading Section
- **Schedule Time**: Set date and time for automatic execution
- **Action**: Choose Buy or Sell
- **Symbol**: Trading symbol for scheduled trade
- **Quantity**: Number of shares/lots
- **Schedule Button**: Adds trade to scheduler

### Right Panel - Orders & Positions

#### Active Orders Section
- **Order Status**: Shows PENDING, FILLED, CANCELLED orders
- **Order Details**: Symbol, quantity, price, and timestamp
- **Real-time Updates**: Automatically refreshes order status

#### Positions Section
- **Open Positions**: Shows current holdings
- **P&L Display**: Profit/Loss for each position (Green/Red)
- **Quantity & Average Price**: Position size and average entry price

#### Scheduled Trades Section
- **Upcoming Trades**: Shows trades waiting for execution
- **Execution Time**: When the trade will be executed
- **Trade Details**: Symbol, action, and quantity

## Trading Operations

### Basic Order Placement

#### Market Order (Immediate Execution)
1. Click "BUY" or "SELL"
2. Enter symbol (e.g., "RELIANCE")
3. Enter quantity (e.g., "100")
4. Leave price as "0" or select "Market" order type
5. Click "Place Order"

#### Limit Order (Specific Price)
1. Click "BUY" or "SELL"
2. Enter symbol and quantity
3. Enter your desired price
4. Select "Limit" order type
5. Click "Place Order"

#### Stop Loss Order
1. Click "BUY" or "SELL"
2. Enter symbol and quantity
3. Enter trigger price in "Price" field
4. Select "Stop Loss" order type
5. Click "Place Order"

### Advanced Order Features

#### Orders with Stop Loss and Target
1. Place a regular order (Market or Limit)
2. Enter "Stop Loss Price" (e.g., 5% below entry for buy orders)
3. Enter "Target Price" (e.g., 10% above entry for buy orders)
4. Click "Place Order"
5. The system will automatically:
   - Monitor the position after order fills
   - Execute stop loss if price moves against you
   - Execute target if price reaches profit level

#### Example: Complete Trade Setup
```
Symbol: RELIANCE
Action: BUY
Quantity: 100
Price: 2500 (limit order)
Stop Loss: 2375 (5% below entry)
Target: 2750 (10% above entry)
```

### Scheduled Trading

#### Setting Up Scheduled Trades
1. In "Scheduled Trading" section:
   - Set "Schedule Time" (future date/time)
   - Choose "Action" (Buy/Sell)
   - Enter "Symbol"
   - Enter "Quantity"
2. Click "Schedule Trade"
3. Trade appears in "Scheduled Trades" section
4. Will execute automatically at specified time

#### Use Cases for Scheduled Trading
- **Market Opening**: Schedule trades for 9:15 AM market open
- **News Events**: Schedule trades around earnings announcements
- **Technical Levels**: Schedule trades when price reaches specific levels
- **Time-based Strategy**: Execute trades at specific times daily

## Advanced Features

### Real-time Market Data

#### Subscribing to Symbols
1. Select appropriate exchange (NSE/BSE/MCX)
2. Enter symbol name
3. Choose instrument type
4. Click "Subscribe to Feed"
5. Live prices appear in Market Data section

#### Understanding Price Display
- **Current Price**: Latest traded price
- **Change**: Absolute price change from previous close
- **Change %**: Percentage change from previous close
- **Color Coding**: Visual indication of price movement
- **Timestamp**: When price was last updated

### Stop Loss and Target Management

#### How It Works
1. **Order Placement**: Place order with stop loss/target
2. **Order Fill**: Wait for main order to execute
3. **Monitoring Activation**: System starts monitoring prices
4. **Automatic Execution**: Stop loss or target triggers automatically
5. **Position Closure**: Opposite order placed to close position

#### Best Practices
- **Stop Loss**: Set 2-5% below entry for buy orders
- **Target**: Set 5-15% above entry for buy orders
- **Risk-Reward**: Maintain at least 1:2 risk-reward ratio
- **Market Conditions**: Adjust levels based on volatility

### Risk Management Features

#### Built-in Protections
- **Daily Loss Limit**: Trading stops if daily loss exceeds limit
- **Position Size Limit**: Prevents oversized positions
- **Order Rate Limiting**: Prevents excessive order placement
- **Circuit Breakers**: Automatic halt on repeated failures

#### Customizing Risk Settings
Edit `js/config.js` file:
```javascript
RISK_MANAGEMENT: {
    MAX_DAILY_LOSS: 50000,        // ₹50,000 daily loss limit
    MAX_POSITION_SIZE: 100000,    // ₹1,00,000 position limit
    MAX_ORDERS_PER_MINUTE: 20,    // 20 orders per minute max
    MAX_ORDERS_PER_DAY: 200       // 200 orders per day max
}
```

## Risk Management

### Understanding Risk Controls

#### Daily Loss Limit
- **Purpose**: Prevents catastrophic losses
- **How it works**: Tracks daily P&L, stops trading if limit exceeded
- **Setting**: Adjust based on your risk tolerance
- **Reset**: Automatically resets daily

#### Position Size Limit
- **Purpose**: Prevents overexposure to single trades
- **Calculation**: Based on order value (quantity × price)
- **Recommendation**: Set to 10-20% of total capital

#### Order Rate Limiting
- **Purpose**: Prevents system abuse and errors
- **Per Minute Limit**: Maximum orders in 60 seconds
- **Daily Limit**: Maximum orders in one day
- **Compliance**: Helps meet exchange requirements

### Best Risk Practices

#### Position Sizing
- **2% Rule**: Risk maximum 2% of capital per trade
- **Diversification**: Don't put all money in one stock
- **Correlation**: Avoid highly correlated positions

#### Stop Loss Strategy
- **Always Use**: Never trade without stop loss
- **Appropriate Size**: 1-5% based on volatility
- **Stick to Plan**: Don't move stop loss against you
- **Quick Execution**: Use market orders for stop loss

#### Capital Management
- **Start Small**: Begin with small position sizes
- **Gradual Increase**: Increase size as you gain experience
- **Emergency Fund**: Keep cash for opportunities
- **Regular Review**: Assess performance weekly

## Troubleshooting

### Common Issues and Solutions

#### Login Problems

**Issue**: "Login Failed" error
**Solutions**:
1. Verify all credentials are correct
2. Check if API access is enabled in Kotak account
3. Ensure stable internet connection
4. Try refreshing the page and logging in again

**Issue**: "Authentication expired"
**Solutions**:
1. Click "Disconnect" then "Connect" again
2. Re-enter credentials if needed
3. Check if session timeout occurred

#### Market Data Issues

**Issue**: Prices not updating
**Solutions**:
1. Check WebSocket connection status
2. Verify symbol is correctly subscribed
3. Ensure symbol exists on selected exchange
4. Refresh page and re-subscribe

**Issue**: "Symbol not found"
**Solutions**:
1. Verify correct symbol format (e.g., "RELIANCE" not "reliance")
2. Check if symbol is available on selected exchange
3. Use search function in Kotak platform to find correct symbol

#### Order Issues

**Issue**: "Order placement failed"
**Solutions**:
1. Check available margin/balance
2. Verify market is open for trading
3. Ensure order parameters are valid
4. Check if daily limits are exceeded

**Issue**: "Stop loss not executing"
**Solutions**:
1. Verify main order was filled first
2. Check if stop loss price is reasonable
3. Ensure WebSocket connection is active
4. Monitor price movements manually

#### Technical Issues

**Issue**: Page not loading properly
**Solutions**:
1. Clear browser cache and cookies
2. Try different browser (Chrome, Firefox, Safari)
3. Disable browser extensions temporarily
4. Check internet connection stability

**Issue**: Notifications not working
**Solutions**:
1. Allow notifications in browser settings
2. Check if sound is enabled
3. Verify notification permissions
4. Test with different notification types

### Getting Help

#### Self-Help Resources
1. **Browser Console**: Press F12 to see detailed error messages
2. **Network Tab**: Check for failed API calls
3. **Local Storage**: Verify credentials are saved
4. **Configuration**: Review config.js settings

#### When to Seek Support
- Persistent login issues after trying all solutions
- Consistent order execution failures
- Unusual system behavior
- Data synchronization problems

#### Support Channels
- **Kotak Neo API Support**: <EMAIL>
- **Technical Documentation**: Tutorial.html file
- **Community Forums**: Trading and programming communities
- **Browser Documentation**: For browser-specific issues

### Performance Optimization

#### Browser Settings
- **Enable JavaScript**: Required for application functionality
- **Allow Cookies**: Needed for session management
- **Disable Ad Blockers**: May interfere with API calls
- **Stable Internet**: Ensure reliable connection

#### System Requirements
- **Modern Browser**: Chrome 80+, Firefox 75+, Safari 13+
- **RAM**: Minimum 4GB for smooth operation
- **Internet Speed**: Broadband connection recommended
- **Screen Resolution**: 1024x768 minimum

---

This user guide covers all aspects of using the Kapil Trading Bot. Remember to always test with small amounts first and gradually increase your trading size as you become comfortable with the system. Happy trading! 📈
