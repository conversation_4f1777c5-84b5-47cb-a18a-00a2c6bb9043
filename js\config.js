// Trading Bot Configuration
const CONFIG = {
    // Kotak Neo API Configuration
    API: {
        BASE_URL: 'https://gw-napi.kotaksecurities.com',
        OAUTH_URL: 'https://napi.kotaksecurities.com/oauth2/token',
        WEBSOCKET_URL: 'wss://mlhsm.kotaksecurities.com',

        // API Endpoints
        ENDPOINTS: {
            // Authentication Flow
            OAUTH_TOKEN: '/oauth2/token',
            LOGIN_TOTP: '/login/1.0/login/v6/totp/login',
            LOGIN_VALIDATE: '/login/1.0/login/v6/totp/validate',
            LOGOUT: '/logout',

            // Trading
            PLACE_ORDER: '/orders',
            MODIFY_ORDER: '/orders',
            CANCEL_ORDER: '/orders',
            ORDER_BOOK: '/orders',
            TRADE_BOOK: '/trades',

            // Portfolio
            POSITIONS: '/positions',
            HOLDINGS: '/holdings',
            LIMITS: '/limits',

            // Market Data
            QUOTES: '/quotes',
            SEARCH_SCRIP: '/instruments',
            MARKET_DATA: '/marketdata'
        },
        
        // Request Headers
        HEADERS: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'neo-fin-key': 'neotradeapi',
            'Authorization': '',
            'consumerKey': '',
            'sid': '',
            'Auth': ''
        }
    },
    
    // WebSocket Configuration
    WEBSOCKET: {
        MARKET_FEED_URL: 'wss://mlhsm.kotaksecurities.com',
        ORDER_UPDATE_URL: 'wss://mlhsm.kotaksecurities.com/orders',
        RECONNECT_INTERVAL: 5000,
        HEARTBEAT_INTERVAL: 30000,
        MAX_RECONNECT_ATTEMPTS: 10
    },
    
    // Trading Configuration
    TRADING: {
        // Order Types
        ORDER_TYPES: {
            MARKET: 'MKT',
            LIMIT: 'LMT',
            STOP_LOSS: 'SL',
            STOP_LOSS_MARKET: 'SL-M'
        },
        
        // Transaction Types
        TRANSACTION_TYPES: {
            BUY: 'B',
            SELL: 'S'
        },
        
        // Product Types
        PRODUCT_TYPES: {
            INTRADAY: 'MIS',
            DELIVERY: 'CNC',
            NORMAL: 'NRML'
        },
        
        // Exchanges
        EXCHANGES: {
            NSE: 'NSE',
            BSE: 'BSE',
            MCX: 'MCX',
            NCDEX: 'NCDEX'
        },
        
        // Instrument Types
        INSTRUMENT_TYPES: {
            EQUITY: 'EQ',
            FUTURES: 'FUT',
            OPTIONS: 'OPT',
            COMMODITY: 'COM'
        },
        
        // Default Settings
        DEFAULT_QUANTITY: 1,
        DEFAULT_PRODUCT: 'MIS',
        MAX_ORDER_VALUE: 1000000,
        MIN_ORDER_VALUE: 1
    },
    
    // Risk Management
    RISK_MANAGEMENT: {
        MAX_DAILY_LOSS: 50000,
        MAX_POSITION_SIZE: 100000,
        STOP_LOSS_PERCENTAGE: 2,
        TARGET_PERCENTAGE: 4,
        MAX_ORDERS_PER_MINUTE: 20,
        MAX_ORDERS_PER_DAY: 200
    },
    
    // Scheduler Configuration
    SCHEDULER: {
        CHECK_INTERVAL: 1000, // 1 second
        MAX_SCHEDULED_TRADES: 100,
        EXECUTION_WINDOW: 30000 // 30 seconds window for execution
    },
    
    // UI Configuration
    UI: {
        REFRESH_INTERVAL: 1000,
        MARKET_DATA_REFRESH: 500,
        ORDER_REFRESH: 2000,
        POSITION_REFRESH: 5000,
        MAX_MARKET_DATA_ITEMS: 20,
        MAX_ORDER_HISTORY: 100,
        NOTIFICATION_TIMEOUT: 5000
    },
    
    // Market Data Configuration
    MARKET_DATA: {
        SUBSCRIPTION_MODES: {
            QUOTE: 'quote',
            SNAP_QUOTE: 'snapquote',
            DEPTH: 'depth'
        },
        
        // Price change thresholds for alerts
        PRICE_CHANGE_THRESHOLDS: {
            MINOR: 0.5,
            MODERATE: 1.0,
            MAJOR: 2.0
        }
    },
    
    // Logging Configuration
    LOGGING: {
        LEVEL: 'INFO', // DEBUG, INFO, WARN, ERROR
        MAX_LOG_ENTRIES: 1000,
        CONSOLE_LOGGING: true,
        FILE_LOGGING: false
    },
    
    // Storage Configuration
    STORAGE: {
        USE_LOCAL_STORAGE: true,
        STORAGE_PREFIX: 'trading_bot_',
        KEYS: {
            USER_CREDENTIALS: 'user_credentials',
            API_CONFIG: 'api_config',
            TRADING_SETTINGS: 'trading_settings',
            SCHEDULED_TRADES: 'scheduled_trades',
            MARKET_SUBSCRIPTIONS: 'market_subscriptions'
        }
    },
    
    // Error Handling
    ERROR_HANDLING: {
        MAX_RETRY_ATTEMPTS: 3,
        RETRY_DELAY: 1000,
        CIRCUIT_BREAKER_THRESHOLD: 5,
        CIRCUIT_BREAKER_TIMEOUT: 60000
    },
    
    // Notification Configuration
    NOTIFICATIONS: {
        ENABLE_BROWSER_NOTIFICATIONS: true,
        ENABLE_SOUND_ALERTS: true,
        SOUND_FILES: {
            ORDER_FILLED: 'sounds/order_filled.mp3',
            STOP_LOSS_HIT: 'sounds/stop_loss.mp3',
            TARGET_REACHED: 'sounds/target.mp3',
            ERROR: 'sounds/error.mp3'
        }
    },
    
    // Performance Monitoring
    PERFORMANCE: {
        ENABLE_METRICS: true,
        METRICS_INTERVAL: 60000,
        MAX_RESPONSE_TIME: 5000,
        ALERT_ON_SLOW_RESPONSE: true
    }
};

// Environment-specific configurations
const ENVIRONMENTS = {
    DEVELOPMENT: {
        API_BASE_URL: 'https://gw-napi.kotaksecurities.com',
        DEBUG_MODE: true,
        MOCK_DATA: false
    },
    
    TESTING: {
        API_BASE_URL: 'https://gw-napi.kotaksecurities.com',
        DEBUG_MODE: true,
        MOCK_DATA: true
    },
    
    PRODUCTION: {
        API_BASE_URL: 'https://gw-napi.kotaksecurities.com',
        DEBUG_MODE: false,
        MOCK_DATA: false
    }
};

// Current environment (change as needed)
const CURRENT_ENVIRONMENT = 'DEVELOPMENT';

// Merge environment-specific config
Object.assign(CONFIG, ENVIRONMENTS[CURRENT_ENVIRONMENT]);

// Utility functions for configuration
const ConfigUtils = {
    // Get configuration value with fallback
    get: (path, fallback = null) => {
        const keys = path.split('.');
        let value = CONFIG;
        
        for (const key of keys) {
            if (value && typeof value === 'object' && key in value) {
                value = value[key];
            } else {
                return fallback;
            }
        }
        
        return value;
    },
    
    // Set configuration value
    set: (path, value) => {
        const keys = path.split('.');
        let obj = CONFIG;
        
        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!(key in obj) || typeof obj[key] !== 'object') {
                obj[key] = {};
            }
            obj = obj[key];
        }
        
        obj[keys[keys.length - 1]] = value;
    },
    
    // Validate required configuration
    validate: () => {
        const required = [
            'API.BASE_URL',
            'WEBSOCKET.MARKET_FEED_URL',
            'TRADING.ORDER_TYPES',
            'RISK_MANAGEMENT.MAX_DAILY_LOSS'
        ];
        
        const missing = [];
        
        for (const path of required) {
            if (ConfigUtils.get(path) === null) {
                missing.push(path);
            }
        }
        
        if (missing.length > 0) {
            throw new Error(`Missing required configuration: ${missing.join(', ')}`);
        }
        
        return true;
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, ConfigUtils };
}
