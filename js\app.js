// Main Application Controller
class TradingBotApp {
    constructor() {
        this.isInitialized = false;
        this.marketDataManager = null;
        this.uiManager = null;
        
        // Initialize the application
        this.init();
    }
    
    async init() {
        try {
            Logger.log('Initializing Trading Bot Application...');
            
            // Initialize UI Manager
            this.uiManager = new UIManager();
            
            // Initialize Market Data Manager
            this.marketDataManager = new MarketDataManager();
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Initialize UI
            this.uiManager.init();
            
            // Check for stored credentials and auto-login
            await this.checkAutoLogin();
            
            this.isInitialized = true;
            Logger.log('Trading Bot Application initialized successfully');
            
        } catch (error) {
            Logger.error('Failed to initialize application', error);
            this.showError('Failed to initialize application: ' + error.message);
        }
    }
    
    setupEventListeners() {
        // API Events
        document.addEventListener('loginSuccess', (event) => {
            this.handleLoginSuccess(event.detail);
        });
        
        document.addEventListener('loginFailed', (event) => {
            this.handleLoginFailed(event.detail);
        });
        
        // WebSocket Events
        document.addEventListener('websocketStatusChange', (event) => {
            this.handleWebSocketStatusChange(event.detail);
        });
        
        // Trading Events
        document.addEventListener('orderPlaced', (event) => {
            this.handleOrderPlaced(event.detail);
        });
        
        document.addEventListener('orderFilled', (event) => {
            this.handleOrderFilled(event.detail);
        });
        
        document.addEventListener('stopLossExecuted', (event) => {
            this.handleStopLossExecuted(event.detail);
        });
        
        document.addEventListener('targetExecuted', (event) => {
            this.handleTargetExecuted(event.detail);
        });
        
        // Scheduler Events
        document.addEventListener('tradeScheduled', (event) => {
            this.handleTradeScheduled(event.detail);
        });
        
        document.addEventListener('tradeExecuted', (event) => {
            this.handleTradeExecuted(event.detail);
        });
        
        // UI Events
        document.getElementById('connectBtn').addEventListener('click', () => {
            this.toggleConnection();
        });
        
        document.getElementById('loginBtn').addEventListener('click', () => {
            this.handleLogin();
        });
        
        document.getElementById('subscribeBtn').addEventListener('click', () => {
            this.handleSubscribeSymbol();
        });
        
        document.getElementById('placeOrderBtn').addEventListener('click', () => {
            this.handlePlaceOrder();
        });
        
        document.getElementById('scheduleTradeBtn').addEventListener('click', () => {
            this.handleScheduleTrade();
        });
        
        // Buy/Sell button events
        document.getElementById('buyBtn').addEventListener('click', () => {
            this.setTransactionType('B');
        });
        
        document.getElementById('sellBtn').addEventListener('click', () => {
            this.setTransactionType('S');
        });
    }
    
    async checkAutoLogin() {
        if (kotakAPI.isAuthenticated) {
            Logger.log('Auto-login detected');
            this.handleLoginSuccess({ autoLogin: true });
        }
    }
    
    async handleLogin() {
        try {
            const consumerKey = document.getElementById('consumerKey').value;
            const consumerSecret = document.getElementById('consumerSecret').value;
            const mobileNumber = document.getElementById('mobileNumber').value;
            const password = document.getElementById('password').value;
            
            if (!consumerKey || !consumerSecret || !mobileNumber || !password) {
                this.showError('Please fill in all login fields');
                return;
            }
            
            this.uiManager.showLoading('Logging in...');
            
            const result = await kotakAPI.login(mobileNumber, password, consumerKey, consumerSecret);
            
            this.uiManager.hideLoading();
            
            if (result.success) {
                this.handleLoginSuccess(result);
            } else {
                this.handleLoginFailed(result);
            }
            
        } catch (error) {
            this.uiManager.hideLoading();
            this.handleLoginFailed({ error: error.message });
        }
    }
    
    handleLoginSuccess(result) {
        Logger.log('Login successful');
        
        // Update UI
        this.uiManager.updateConnectionStatus(true);
        this.uiManager.showSuccess('Login successful');
        
        // Connect WebSockets
        this.connectWebSockets();
        
        // Start data refresh
        this.startDataRefresh();
        
        // Dispatch event
        const event = new CustomEvent('loginSuccess', { detail: result });
        document.dispatchEvent(event);
    }
    
    handleLoginFailed(result) {
        Logger.error('Login failed', result.error);
        
        // Update UI
        this.uiManager.updateConnectionStatus(false);
        this.uiManager.showError('Login failed: ' + result.error);
    }
    
    connectWebSockets() {
        try {
            wsManager.connectMarketData();
            wsManager.connectOrderUpdates();
        } catch (error) {
            Logger.error('Failed to connect WebSockets', error);
            this.showError('Failed to connect to real-time data');
        }
    }
    
    handleWebSocketStatusChange(detail) {
        const { type, connected } = detail;
        
        if (type === 'marketData') {
            this.uiManager.updateMarketDataStatus(connected);
        }
        
        Logger.log(`WebSocket ${type} ${connected ? 'connected' : 'disconnected'}`);
    }
    
    toggleConnection() {
        if (kotakAPI.isAuthenticated) {
            this.disconnect();
        } else {
            this.handleLogin();
        }
    }
    
    async disconnect() {
        try {
            this.uiManager.showLoading('Disconnecting...');
            
            // Disconnect WebSockets
            wsManager.disconnect();
            
            // Logout from API
            await kotakAPI.logout();
            
            // Update UI
            this.uiManager.updateConnectionStatus(false);
            this.uiManager.showInfo('Disconnected successfully');
            
            this.uiManager.hideLoading();
            
        } catch (error) {
            this.uiManager.hideLoading();
            Logger.error('Disconnect failed', error);
            this.showError('Disconnect failed: ' + error.message);
        }
    }
    
    handleSubscribeSymbol() {
        try {
            const exchange = document.getElementById('exchangeSelect').value;
            const symbol = document.getElementById('symbolInput').value.trim().toUpperCase();
            const instrumentType = document.getElementById('instrumentType').value;
            
            if (!symbol) {
                this.showError('Please enter a symbol');
                return;
            }
            
            // Subscribe to market data
            wsManager.subscribeToSymbol(symbol, exchange, 'quote');
            
            // Add to market data manager
            this.marketDataManager.addSymbol(symbol, exchange, instrumentType);
            
            // Clear input
            document.getElementById('symbolInput').value = '';
            
            this.uiManager.showSuccess(`Subscribed to ${symbol} on ${exchange}`);
            
        } catch (error) {
            Logger.error('Failed to subscribe to symbol', error);
            this.showError('Failed to subscribe to symbol: ' + error.message);
        }
    }
    
    setTransactionType(type) {
        this.currentTransactionType = type;
        
        // Update UI
        const buyBtn = document.getElementById('buyBtn');
        const sellBtn = document.getElementById('sellBtn');
        
        if (type === 'B') {
            buyBtn.classList.add('active');
            sellBtn.classList.remove('active');
        } else {
            sellBtn.classList.add('active');
            buyBtn.classList.remove('active');
        }
    }
    
    async handlePlaceOrder() {
        try {
            if (!this.currentTransactionType) {
                this.showError('Please select Buy or Sell');
                return;
            }
            
            const orderParams = {
                symbol: document.getElementById('orderSymbol').value.trim().toUpperCase(),
                exchange: document.getElementById('exchangeSelect').value,
                transactionType: this.currentTransactionType,
                quantity: parseInt(document.getElementById('orderQuantity').value),
                orderType: document.getElementById('orderType').value,
                price: parseFloat(document.getElementById('orderPrice').value) || 0,
                stopLoss: parseFloat(document.getElementById('stopLossPrice').value) || null,
                target: parseFloat(document.getElementById('targetPrice').value) || null,
                product: CONFIG.TRADING.DEFAULT_PRODUCT
            };
            
            if (!orderParams.symbol || !orderParams.quantity) {
                this.showError('Please fill in symbol and quantity');
                return;
            }
            
            this.uiManager.showLoading('Placing order...');
            
            const result = await tradingManager.placeOrder(orderParams);
            
            this.uiManager.hideLoading();
            
            if (result.success) {
                this.uiManager.showSuccess('Order placed successfully');
                this.clearOrderForm();
            } else {
                this.showError('Order failed: ' + result.error);
            }
            
        } catch (error) {
            this.uiManager.hideLoading();
            Logger.error('Failed to place order', error);
            this.showError('Failed to place order: ' + error.message);
        }
    }
    
    handleScheduleTrade() {
        try {
            const scheduleParams = {
                symbol: document.getElementById('scheduleSymbol').value.trim().toUpperCase(),
                exchange: document.getElementById('exchangeSelect').value,
                transactionType: document.getElementById('scheduleAction').value,
                quantity: parseInt(document.getElementById('scheduleQuantity').value),
                scheduledTime: document.getElementById('scheduleTime').value,
                orderType: 'MKT',
                product: CONFIG.TRADING.DEFAULT_PRODUCT
            };
            
            if (!scheduleParams.symbol || !scheduleParams.quantity || !scheduleParams.scheduledTime) {
                this.showError('Please fill in all schedule fields');
                return;
            }
            
            const result = tradingScheduler.scheduleTradeExecution(scheduleParams);
            
            if (result.success) {
                this.uiManager.showSuccess('Trade scheduled successfully');
                this.clearScheduleForm();
            } else {
                this.showError('Schedule failed: ' + result.error);
            }
            
        } catch (error) {
            Logger.error('Failed to schedule trade', error);
            this.showError('Failed to schedule trade: ' + error.message);
        }
    }
    
    clearOrderForm() {
        document.getElementById('orderSymbol').value = '';
        document.getElementById('orderQuantity').value = '';
        document.getElementById('orderPrice').value = '';
        document.getElementById('stopLossPrice').value = '';
        document.getElementById('targetPrice').value = '';
        
        // Reset transaction type
        this.currentTransactionType = null;
        document.getElementById('buyBtn').classList.remove('active');
        document.getElementById('sellBtn').classList.remove('active');
    }
    
    clearScheduleForm() {
        document.getElementById('scheduleSymbol').value = '';
        document.getElementById('scheduleQuantity').value = '';
        document.getElementById('scheduleTime').value = '';
    }
    
    startDataRefresh() {
        // Refresh orders every 5 seconds
        setInterval(() => {
            this.refreshOrders();
        }, 5000);
        
        // Refresh positions every 10 seconds
        setInterval(() => {
            this.refreshPositions();
        }, 10000);
        
        // Update UI stats every second
        setInterval(() => {
            this.updateStats();
        }, 1000);
    }
    
    async refreshOrders() {
        try {
            const orders = tradingManager.getActiveOrders();
            this.uiManager.updateActiveOrders(orders);
        } catch (error) {
            Logger.error('Failed to refresh orders', error);
        }
    }
    
    async refreshPositions() {
        try {
            const positions = tradingManager.getPositions();
            this.uiManager.updatePositions(positions);
        } catch (error) {
            Logger.error('Failed to refresh positions', error);
        }
    }
    
    updateStats() {
        try {
            const stats = tradingManager.getDailyStats();
            const schedulerStats = tradingScheduler.getStatistics();
            
            this.uiManager.updateStats({
                ...stats,
                scheduledTrades: schedulerStats.scheduled
            });
            
            // Update last update time
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
            
        } catch (error) {
            Logger.error('Failed to update stats', error);
        }
    }
    
    // Event Handlers
    handleOrderPlaced(detail) {
        this.uiManager.showSuccess(`Order placed: ${detail.orderId}`);
        this.refreshOrders();
    }
    
    handleOrderFilled(detail) {
        this.uiManager.showSuccess(`Order filled: ${detail.orderId}`);
        this.refreshOrders();
        this.refreshPositions();
    }
    
    handleStopLossExecuted(detail) {
        this.uiManager.showWarning(`Stop loss executed at ₹${detail.price}`);
        this.refreshOrders();
        this.refreshPositions();
    }
    
    handleTargetExecuted(detail) {
        this.uiManager.showSuccess(`Target reached at ₹${detail.price}`);
        this.refreshOrders();
        this.refreshPositions();
    }
    
    handleTradeScheduled(detail) {
        this.uiManager.showInfo(`Trade scheduled: ${detail.trade.symbol}`);
        this.updateScheduledTrades();
    }
    
    handleTradeExecuted(detail) {
        this.uiManager.showSuccess(`Scheduled trade executed: ${detail.trade.symbol}`);
        this.updateScheduledTrades();
        this.refreshOrders();
    }
    
    updateScheduledTrades() {
        try {
            const scheduledTrades = tradingScheduler.getScheduledTrades();
            this.uiManager.updateScheduledTrades(scheduledTrades);
        } catch (error) {
            Logger.error('Failed to update scheduled trades', error);
        }
    }
    
    // Utility Methods
    showError(message) {
        this.uiManager.showError(message);
    }
    
    showSuccess(message) {
        this.uiManager.showSuccess(message);
    }
    
    showInfo(message) {
        this.uiManager.showInfo(message);
    }
}

// Market Data Manager
class MarketDataManager {
    constructor() {
        this.subscribedSymbols = new Map();
        this.lastPrices = new Map();
    }
    
    addSymbol(symbol, exchange, instrumentType) {
        const key = `${exchange}:${symbol}`;
        this.subscribedSymbols.set(key, {
            symbol,
            exchange,
            instrumentType,
            lastPrice: 0,
            change: 0,
            changePercent: 0,
            lastUpdate: new Date()
        });
        
        this.updateMarketDataDisplay();
    }
    
    removeSymbol(symbol, exchange) {
        const key = `${exchange}:${symbol}`;
        this.subscribedSymbols.delete(key);
        wsManager.unsubscribeFromSymbol(symbol, exchange);
        this.updateMarketDataDisplay();
    }
    
    updateQuote(data) {
        const key = `${data.exchange}:${data.symbol}`;
        const symbolData = this.subscribedSymbols.get(key);
        
        if (symbolData) {
            const newPrice = parseFloat(data.ltp || data.price);
            const oldPrice = symbolData.lastPrice;
            
            symbolData.lastPrice = newPrice;
            symbolData.change = newPrice - oldPrice;
            symbolData.changePercent = oldPrice ? ((newPrice - oldPrice) / oldPrice) * 100 : 0;
            symbolData.lastUpdate = new Date();
            
            this.updateMarketDataDisplay();
        }
    }
    
    updateMarketDataDisplay() {
        const container = document.getElementById('marketDataContainer');
        
        if (this.subscribedSymbols.size === 0) {
            container.innerHTML = '<p class="text-muted text-center">No symbols subscribed</p>';
            return;
        }
        
        let html = '';
        for (const [key, data] of this.subscribedSymbols) {
            const changeClass = data.change > 0 ? 'price-up' : data.change < 0 ? 'price-down' : 'price-neutral';
            const changeIcon = data.change > 0 ? '▲' : data.change < 0 ? '▼' : '●';
            
            html += `
                <div class="market-data-item slide-in">
                    <div class="symbol-name">${data.symbol}</div>
                    <div class="price-info">
                        <span class="current-price">₹${data.lastPrice.toFixed(2)}</span>
                        <span class="price-change ${changeClass}">
                            ${changeIcon} ${data.change.toFixed(2)} (${data.changePercent.toFixed(2)}%)
                        </span>
                    </div>
                    <small class="text-muted">${data.exchange} • ${data.lastUpdate.toLocaleTimeString()}</small>
                </div>
            `;
        }
        
        container.innerHTML = html;
    }
}

// UI Manager
class UIManager {
    constructor() {
        this.notifications = [];
    }
    
    init() {
        // Initialize UI components
        this.updateConnectionStatus(false);
        this.setupNotifications();
    }
    
    setupNotifications() {
        // Request notification permission
        if ('Notification' in window && CONFIG.NOTIFICATIONS.ENABLE_BROWSER_NOTIFICATIONS) {
            Notification.requestPermission();
        }
    }
    
    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connectionStatus');
        const connectBtn = document.getElementById('connectBtn');
        
        if (connected) {
            statusElement.innerHTML = '<i class="fas fa-circle text-success"></i> Connected';
            connectBtn.textContent = 'Disconnect';
            connectBtn.className = 'btn btn-outline-danger btn-sm';
        } else {
            statusElement.innerHTML = '<i class="fas fa-circle text-danger"></i> Disconnected';
            connectBtn.textContent = 'Connect';
            connectBtn.className = 'btn btn-outline-light btn-sm';
        }
    }
    
    updateMarketDataStatus(connected) {
        // Update market data connection indicator
        const indicator = document.querySelector('.market-data-indicator');
        if (indicator) {
            indicator.className = connected ? 'text-success' : 'text-danger';
        }
    }
    
    showLoading(message = 'Loading...') {
        // Show loading spinner
        const loadingHtml = `
            <div class="loading-overlay">
                <div class="spinner"></div>
                ${message}
            </div>
        `;
        
        // Add to body or specific container
        const overlay = document.createElement('div');
        overlay.innerHTML = loadingHtml;
        overlay.className = 'loading-container';
        document.body.appendChild(overlay);
    }
    
    hideLoading() {
        const loadingContainer = document.querySelector('.loading-container');
        if (loadingContainer) {
            loadingContainer.remove();
        }
    }
    
    showNotification(message, type = 'info', duration = 5000) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after duration
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
        
        // Browser notification
        if ('Notification' in window && Notification.permission === 'granted' && 
            CONFIG.NOTIFICATIONS.ENABLE_BROWSER_NOTIFICATIONS) {
            new Notification('Trading Bot', {
                body: message,
                icon: '/favicon.ico'
            });
        }
    }
    
    showSuccess(message) {
        this.showNotification(message, 'success');
    }
    
    showError(message) {
        this.showNotification(message, 'danger', 8000);
    }
    
    showWarning(message) {
        this.showNotification(message, 'warning');
    }
    
    showInfo(message) {
        this.showNotification(message, 'info');
    }
    
    updateActiveOrders(orders) {
        const container = document.getElementById('activeOrdersContainer');
        
        if (orders.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">No active orders</p>';
            return;
        }
        
        let html = '';
        for (const order of orders) {
            const statusClass = order.status === 'PENDING' ? 'status-pending' : 
                               order.status === 'FILLED' ? 'status-filled' : 'status-cancelled';
            
            html += `
                <div class="order-item">
                    <div class="order-header">
                        <span class="order-symbol">${order.symbol}</span>
                        <span class="order-status ${statusClass}">${order.status}</span>
                    </div>
                    <div class="order-details">
                        ${order.transactionType === 'B' ? 'BUY' : 'SELL'} ${order.quantity} @ ₹${order.price || 'MKT'}
                    </div>
                    <small class="text-muted">${order.timestamp.toLocaleTimeString()}</small>
                </div>
            `;
        }
        
        container.innerHTML = html;
    }
    
    updatePositions(positions) {
        const container = document.getElementById('positionsContainer');
        
        if (positions.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">No positions</p>';
            return;
        }
        
        let html = '';
        for (const position of positions) {
            const pnlClass = position.pnl >= 0 ? 'pnl-positive' : 'pnl-negative';
            
            html += `
                <div class="position-item">
                    <div class="order-header">
                        <span class="order-symbol">${position.symbol}</span>
                        <span class="position-pnl ${pnlClass}">₹${position.pnl.toFixed(2)}</span>
                    </div>
                    <div class="order-details">
                        Qty: ${position.quantity} | Avg: ₹${position.averagePrice.toFixed(2)}
                    </div>
                </div>
            `;
        }
        
        container.innerHTML = html;
    }
    
    updateScheduledTrades(trades) {
        const container = document.getElementById('scheduledTradesContainer');
        
        if (trades.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">No scheduled trades</p>';
            return;
        }
        
        let html = '';
        for (const trade of trades) {
            html += `
                <div class="scheduled-trade-item">
                    <div class="schedule-time">${trade.scheduledTime.toLocaleString()}</div>
                    <div class="schedule-action">
                        ${trade.transactionType === 'B' ? 'BUY' : 'SELL'} ${trade.quantity} ${trade.symbol}
                    </div>
                    <small class="text-muted">${trade.status}</small>
                </div>
            `;
        }
        
        container.innerHTML = html;
    }
    
    updateStats(stats) {
        document.getElementById('ordersToday').textContent = stats.ordersPlaced || 0;
        document.getElementById('totalPnL').textContent = `₹${(stats.totalPnL || 0).toFixed(2)}`;
        
        // Update bot status
        const statusText = kotakAPI.isAuthenticated ? 'Active' : 'Idle';
        document.getElementById('botStatus').textContent = statusText;
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Validate configuration
    try {
        ConfigUtils.validate();
        Logger.log('Configuration validated successfully');
    } catch (error) {
        Logger.error('Configuration validation failed', error);
        alert('Configuration error: ' + error.message);
        return;
    }
    
    // Create global app instance
    window.tradingBotApp = new TradingBotApp();
    
    Logger.log('Trading Bot Application started');
});
