<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kapil Trading Bot - Automated Trading Platform</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <header class="navbar navbar-dark bg-dark">
            <div class="container-fluid">
                <span class="navbar-brand mb-0 h1">
                    <i class="fas fa-robot"></i> Kapil Trading Bot
                </span>
                <div class="d-flex align-items-center">
                    <span class="text-light me-3" id="connectionStatus">
                        <i class="fas fa-circle text-danger"></i> Disconnected
                    </span>
                    <button class="btn btn-outline-light btn-sm" id="connectBtn">Connect</button>
                </div>
            </div>
        </header>

        <!-- Main Dashboard -->
        <div class="row mt-3">
            <!-- Left Panel - Market Data & Controls -->
            <div class="col-md-4">
                <!-- API Configuration -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5><i class="fas fa-cog"></i> API Configuration</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Consumer Key</label>
                            <input type="text" class="form-control" id="consumerKey" placeholder="Enter Consumer Key">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Consumer Secret</label>
                            <input type="password" class="form-control" id="consumerSecret" placeholder="Enter Consumer Secret">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Mobile Number</label>
                            <input type="text" class="form-control" id="mobileNumber" placeholder="Enter Mobile Number (with +91)">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">UCC</label>
                            <input type="text" class="form-control" id="ucc" placeholder="Enter UCC">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">TOTP</label>
                            <input type="text" class="form-control" id="totp" placeholder="Enter 6-digit TOTP" maxlength="6">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">MPIN</label>
                            <input type="password" class="form-control" id="mpin" placeholder="Enter 4-digit MPIN" maxlength="4">
                        </div>
                        <button class="btn btn-primary w-100" id="loginBtn">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </button>
                    </div>
                </div>

                <!-- Symbol Selection -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line"></i> Symbol Selection</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Exchange</label>
                            <select class="form-select" id="exchangeSelect">
                                <option value="NSE">NSE (Equity/Options)</option>
                                <option value="MCX">MCX (Commodity)</option>
                                <option value="BSE">BSE (Equity)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Symbol</label>
                            <input type="text" class="form-control" id="symbolInput" placeholder="e.g., RELIANCE, NIFTY">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Instrument Type</label>
                            <select class="form-select" id="instrumentType">
                                <option value="EQ">Equity</option>
                                <option value="FUT">Futures</option>
                                <option value="OPT">Options</option>
                                <option value="COM">Commodity</option>
                            </select>
                        </div>
                        <button class="btn btn-success w-100" id="subscribeBtn" disabled>
                            <i class="fas fa-plus"></i> Subscribe to Feed
                        </button>
                    </div>
                </div>

                <!-- Live Market Data -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-broadcast-tower"></i> Live Market Data</h5>
                    </div>
                    <div class="card-body">
                        <div id="marketDataContainer">
                            <p class="text-muted text-center">No symbols subscribed</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Center Panel - Trading Interface -->
            <div class="col-md-4">
                <!-- Order Placement -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5><i class="fas fa-shopping-cart"></i> Place Order</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-6">
                                <button class="btn btn-success w-100" id="buyBtn" disabled>BUY</button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-danger w-100" id="sellBtn" disabled>SELL</button>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Symbol</label>
                            <input type="text" class="form-control" id="orderSymbol" placeholder="Trading Symbol">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Quantity</label>
                            <input type="number" class="form-control" id="orderQuantity" placeholder="Quantity">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Price</label>
                            <input type="number" class="form-control" id="orderPrice" placeholder="Price (0 for Market Order)" step="0.01">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Order Type</label>
                            <select class="form-select" id="orderType">
                                <option value="MKT">Market</option>
                                <option value="LMT">Limit</option>
                                <option value="SL">Stop Loss</option>
                                <option value="SL-M">Stop Loss Market</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Stop Loss Price</label>
                            <input type="number" class="form-control" id="stopLossPrice" placeholder="Stop Loss Price" step="0.01">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Target Price</label>
                            <input type="number" class="form-control" id="targetPrice" placeholder="Target Price" step="0.01">
                        </div>
                        <button class="btn btn-primary w-100" id="placeOrderBtn" disabled>
                            <i class="fas fa-paper-plane"></i> Place Order
                        </button>
                    </div>
                </div>

                <!-- Scheduled Trading -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-clock"></i> Scheduled Trading</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Schedule Time</label>
                            <input type="datetime-local" class="form-control" id="scheduleTime">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Action</label>
                            <select class="form-select" id="scheduleAction">
                                <option value="BUY">Buy</option>
                                <option value="SELL">Sell</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Symbol</label>
                            <input type="text" class="form-control" id="scheduleSymbol" placeholder="Symbol">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Quantity</label>
                            <input type="number" class="form-control" id="scheduleQuantity" placeholder="Quantity">
                        </div>
                        <button class="btn btn-warning w-100" id="scheduleTradeBtn" disabled>
                            <i class="fas fa-calendar-plus"></i> Schedule Trade
                        </button>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Orders & Positions -->
            <div class="col-md-4">
                <!-- Active Orders -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Active Orders</h5>
                    </div>
                    <div class="card-body">
                        <div id="activeOrdersContainer">
                            <p class="text-muted text-center">No active orders</p>
                        </div>
                    </div>
                </div>

                <!-- Positions -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5><i class="fas fa-briefcase"></i> Positions</h5>
                    </div>
                    <div class="card-body">
                        <div id="positionsContainer">
                            <p class="text-muted text-center">No positions</p>
                        </div>
                    </div>
                </div>

                <!-- Scheduled Trades -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-calendar-alt"></i> Scheduled Trades</h5>
                    </div>
                    <div class="card-body">
                        <div id="scheduledTradesContainer">
                            <p class="text-muted text-center">No scheduled trades</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Bar -->
        <footer class="bg-light mt-4 p-3">
            <div class="row">
                <div class="col-md-3">
                    <small class="text-muted">Status: <span id="botStatus">Idle</span></small>
                </div>
                <div class="col-md-3">
                    <small class="text-muted">Last Update: <span id="lastUpdate">Never</span></small>
                </div>
                <div class="col-md-3">
                    <small class="text-muted">Orders Today: <span id="ordersToday">0</span></small>
                </div>
                <div class="col-md-3">
                    <small class="text-muted">P&L: <span id="totalPnL">₹0.00</span></small>
                </div>
            </div>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/websocket.js"></script>
    <script src="js/trading.js"></script>
    <script src="js/scheduler.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
