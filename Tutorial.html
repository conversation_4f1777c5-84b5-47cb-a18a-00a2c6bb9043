<!DOCTYPE html>
<!-- saved from url=(0093)https://documenter.getpostman.com/view/21534797/UzBnqmpD#c92c252d-b435-4133-8a65-16324a02ee3c -->
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><meta name="top-bar" content="FFFFFF"><meta name="highlight" content="9c274c"><meta name="right-sidebar" content="1c3c72"> <meta name="logo" content="https://res.cloudinary.com/postman/image/upload/t_team_logo_pubdoc/v1/team/cabd3943ebad0b4a5db5d68d34422d62867bc3333d87582ef4b610027e188911"> <meta name="logo-dark" content="https://res.cloudinary.com/postman/image/upload/t_team_logo_pubdoc/v1/team/cabd3943ebad0b4a5db5d68d34422d62867bc3333d87582ef4b610027e188911"><meta name="run-js" content="https://run.pstmn.io/button.js"><meta name="environmentUID" content="-"><meta name="isEnvFetchError" content="false"><meta name="collection-info-public" content="true"><meta name="collection-isPublicCollection" content="false"><meta name="robots" content="noindex,nofollow"> <meta name="ownerId" content="21534797"><meta name="publishedId" content="UzBnqmpD"><meta name="collectionId" content="21534797-1395ba24-126c-4b7f-859b-0ba5934fc416"><meta name="versionTagId" content="latest"><link rel="preconnect" href="https://documenter.gw.postman.com/"><link rel="preconnect" href="https://documenter-assets.pstmn.io/"><link rel="prefetch" href="https://documenter.gw.postman.com/view/metadata/UzBnqmpD" crossorigin="anonymous"><link rel="prefetch" href="https://documenter.gw.postman.com/api/collections/21534797/UzBnqmpD?segregateAuth=true&amp;versionTag=latest" crossorigin="anonymous"><link rel="canonical" href="https://documenter.getpostman.com/view/21534797/UzBnqmpD"> <meta name="description" content="## Welcome to Kotak NEO APIs - the future of trading

Build your own Trading Platform Today

Kotak Neo’s powerful REST APIs can help you develop your own trading Terminal and implement your strategies easily and intuitively. Our APIs give you open access to live market feeds, orders, live positions, and much more.

## What are the benefits of our APIs?

#### Absolutely Free, No strings attached.

We charge you absolutely nothing! Build your own trading terminal at no cost!

#### User friendly

Our state-of-the-art APIs help you create user-friendly end-to-end broking services without breaking a sweat.

#### Our Service

With years of experience, we’re proud to offer a truly market-leading service. You will also be associated with a well-established brand and a full-service broker.

#### Dependable support

You can count on our dedicated support team for a quick turnaround for all your queries.

#### Real-time execution

Execute real-time transactions supported by live data feeds.

#### Fast and seamless

You can place up to 20 orders per second and 200 orders per min."><meta name="documentationLayout" content="classic-double-column"><meta name="generator" content="Postman Documenter"><title>Kotak Neo Trade API 1.0.0</title> <meta name="languages" content="[{&quot;key&quot;:&quot;csharp&quot;,&quot;label&quot;:&quot;C#&quot;,&quot;variant&quot;:&quot;HttpClient&quot;},{&quot;key&quot;:&quot;csharp&quot;,&quot;label&quot;:&quot;C#&quot;,&quot;variant&quot;:&quot;RestSharp&quot;},{&quot;key&quot;:&quot;curl&quot;,&quot;label&quot;:&quot;cURL&quot;,&quot;variant&quot;:&quot;cURL&quot;},{&quot;key&quot;:&quot;dart&quot;,&quot;label&quot;:&quot;Dart&quot;,&quot;variant&quot;:&quot;http&quot;},{&quot;key&quot;:&quot;go&quot;,&quot;label&quot;:&quot;Go&quot;,&quot;variant&quot;:&quot;Native&quot;},{&quot;key&quot;:&quot;http&quot;,&quot;label&quot;:&quot;HTTP&quot;,&quot;variant&quot;:&quot;HTTP&quot;},{&quot;key&quot;:&quot;java&quot;,&quot;label&quot;:&quot;Java&quot;,&quot;variant&quot;:&quot;OkHttp&quot;},{&quot;key&quot;:&quot;java&quot;,&quot;label&quot;:&quot;Java&quot;,&quot;variant&quot;:&quot;Unirest&quot;},{&quot;key&quot;:&quot;javascript&quot;,&quot;label&quot;:&quot;JavaScript&quot;,&quot;variant&quot;:&quot;Fetch&quot;},{&quot;key&quot;:&quot;javascript&quot;,&quot;label&quot;:&quot;JavaScript&quot;,&quot;variant&quot;:&quot;jQuery&quot;},{&quot;key&quot;:&quot;javascript&quot;,&quot;label&quot;:&quot;JavaScript&quot;,&quot;variant&quot;:&quot;XHR&quot;},{&quot;key&quot;:&quot;c&quot;,&quot;label&quot;:&quot;C&quot;,&quot;variant&quot;:&quot;libcurl&quot;},{&quot;key&quot;:&quot;nodejs&quot;,&quot;label&quot;:&quot;NodeJs&quot;,&quot;variant&quot;:&quot;Axios&quot;},{&quot;key&quot;:&quot;nodejs&quot;,&quot;label&quot;:&quot;NodeJs&quot;,&quot;variant&quot;:&quot;Native&quot;},{&quot;key&quot;:&quot;nodejs&quot;,&quot;label&quot;:&quot;NodeJs&quot;,&quot;variant&quot;:&quot;Request&quot;},{&quot;key&quot;:&quot;nodejs&quot;,&quot;label&quot;:&quot;NodeJs&quot;,&quot;variant&quot;:&quot;Unirest&quot;},{&quot;key&quot;:&quot;objective-c&quot;,&quot;label&quot;:&quot;Objective-C&quot;,&quot;variant&quot;:&quot;NSURLSession&quot;},{&quot;key&quot;:&quot;ocaml&quot;,&quot;label&quot;:&quot;OCaml&quot;,&quot;variant&quot;:&quot;Cohttp&quot;},{&quot;key&quot;:&quot;php&quot;,&quot;label&quot;:&quot;PHP&quot;,&quot;variant&quot;:&quot;cURL&quot;},{&quot;key&quot;:&quot;php&quot;,&quot;label&quot;:&quot;PHP&quot;,&quot;variant&quot;:&quot;Guzzle&quot;},{&quot;key&quot;:&quot;php&quot;,&quot;label&quot;:&quot;PHP&quot;,&quot;variant&quot;:&quot;HTTP_Request2&quot;},{&quot;key&quot;:&quot;php&quot;,&quot;label&quot;:&quot;PHP&quot;,&quot;variant&quot;:&quot;pecl_http&quot;},{&quot;key&quot;:&quot;powershell&quot;,&quot;label&quot;:&quot;PowerShell&quot;,&quot;variant&quot;:&quot;RestMethod&quot;},{&quot;key&quot;:&quot;python&quot;,&quot;label&quot;:&quot;Python&quot;,&quot;variant&quot;:&quot;http.client&quot;},{&quot;key&quot;:&quot;python&quot;,&quot;label&quot;:&quot;Python&quot;,&quot;variant&quot;:&quot;Requests&quot;},{&quot;key&quot;:&quot;r&quot;,&quot;label&quot;:&quot;R&quot;,&quot;variant&quot;:&quot;httr&quot;},{&quot;key&quot;:&quot;r&quot;,&quot;label&quot;:&quot;R&quot;,&quot;variant&quot;:&quot;RCurl&quot;},{&quot;key&quot;:&quot;ruby&quot;,&quot;label&quot;:&quot;Ruby&quot;,&quot;variant&quot;:&quot;Net::HTTP&quot;},{&quot;key&quot;:&quot;shell&quot;,&quot;label&quot;:&quot;Shell&quot;,&quot;variant&quot;:&quot;Httpie&quot;},{&quot;key&quot;:&quot;shell&quot;,&quot;label&quot;:&quot;Shell&quot;,&quot;variant&quot;:&quot;wget&quot;},{&quot;key&quot;:&quot;swift&quot;,&quot;label&quot;:&quot;Swift&quot;,&quot;variant&quot;:&quot;URLSession&quot;}]"><link rel="stylesheet" href="./Kotak Neo Trade API 1.0.0_files/importer.0b9c256e370f9b1206e0.css"><link rel="shortcut icon" href="https://documenter-assets.pstmn.io/favicon.ico">  <meta property="og:title" content="Kotak Neo Trade API 1.0.0"> <meta property="og:description" content="## Welcome to Kotak NEO APIs - the future of trading

Build your own Trading Platform Today

Kotak Neo’s powerful REST APIs can help you develop your own trading Terminal and implement your strategies easily and intuitively. Our APIs give you open access to live market feeds, orders, live positions, and much more.

## What are the benefits of our APIs?

#### Absolutely Free, No strings attached.

We charge you absolutely nothing! Build your own trading terminal at no cost!

#### User friendly

Our state-of-the-art APIs help you create user-friendly end-to-end broking services without breaking a sweat.

#### Our Service

With years of experience, we’re proud to offer a truly market-leading service. You will also be associated with a well-established brand and a full-service broker.

#### Dependable support

You can count on our dedicated support team for a quick turnaround for all your queries.

#### Real-time execution

Execute real-time transactions supported by live data feeds.

####..."> <meta property="og:site_name" content="Kotak Neo Trade API 1.0.0"> <meta property="og:url" content="https://documenter.getpostman.com/view/21534797/UzBnqmpD"> <meta property="og:image" content="https://res.cloudinary.com/postman/image/upload/t_team_logo_pubdoc/v1/team/cabd3943ebad0b4a5db5d68d34422d62867bc3333d87582ef4b610027e188911">  <meta name="twitter:title" value="Kotak Neo Trade API 1.0.0"> <meta name="twitter:description" value="## Welcome to Kotak NEO APIs - the future of trading

Build your own Trading Platform Today

Kotak Neo’s powerful REST APIs can help you develop your own trading Terminal and implement your strategies easily and intuitively. Our APIs give you open access to live market feeds, orders, live positions, and much more.

## What are the benefits of our APIs?

#### Absolutely Free, No strings attached.

We charge you absolutely nothing! Build your own trading terminal at no cost!

#### User friendly

Our state-of-the-art APIs help you create user-friendly end-to-end broking services without breaking a sweat.

#### Our Service

With years of experience, we’re proud to offer a truly market-leading service. You will also be associated with a well-established brand and a full-service broker.

#### Dependable support

You can count on our dedicated support team for a quick turnaround for all your queries.

#### Real-time execution

Execute real-time transactions supported by live data feeds.

####..."><meta name="twitter:card" content="summary"><meta name="twitter:domain" value="https://documenter.getpostman.com/view/21534797/UzBnqmpD"> <meta name="twitter:image" content="https://res.cloudinary.com/postman/image/upload/t_team_logo_pubdoc/v1/team/cabd3943ebad0b4a5db5d68d34422d62867bc3333d87582ef4b610027e188911"><meta name="twitter:label1" value="Last Update"><meta name="twitter:data1" value=""> <script type="text/javascript" src="./Kotak Neo Trade API 1.0.0_files/NRJS-8482e4e3e1750395f5d"></script><script src="./Kotak Neo Trade API 1.0.0_files/nr-spa-1216.min.js.download"></script><script nonce="">;window.NREUM||(NREUM={});NREUM.init={distributed_tracing:{enabled:true},privacy:{cookies_enabled:true},ajax:{deny_list:["bam.nr-data.net"]}};

      ;NREUM.loader_config={accountID:"2665918",trustKey:"2665918",agentID:"**********",licenseKey:"NRJS-8482e4e3e1750395f5d",applicationID:"**********"}
      ;NREUM.info={beacon:"bam.nr-data.net",errorBeacon:"bam.nr-data.net",licenseKey:"NRJS-8482e4e3e1750395f5d",applicationID:"**********",sa:1}
      window.NREUM||(NREUM={}),__nr_require=function(t,e,n){function r(n){if(!e[n]){var o=e[n]={exports:{}};t[n][0].call(o.exports,function(e){var o=t[n][1][e];return r(o||e)},o,o.exports)}return e[n].exports}if("function"==typeof __nr_require)return __nr_require;for(var o=0;o<n.length;o++)r(n[o]);return r}({1:[function(t,e,n){function r(t){try{s.console&&console.log(t)}catch(e){}}var o,i=t("ee"),a=t(31),s={};try{o=localStorage.getItem("__nr_flags").split(","),console&&"function"==typeof console.log&&(s.console=!0,o.indexOf("dev")!==-1&&(s.dev=!0),o.indexOf("nr_dev")!==-1&&(s.nrDev=!0))}catch(c){}s.nrDev&&i.on("internal-error",function(t){r(t.stack)}),s.dev&&i.on("fn-err",function(t,e,n){r(n.stack)}),s.dev&&(r("NR AGENT IN DEVELOPMENT MODE"),r("flags: "+a(s,function(t,e){return t}).join(", ")))},{}],2:[function(t,e,n){function r(t,e,n,r,s){try{l?l-=1:o(s||new UncaughtException(t,e,n),!0)}catch(f){try{i("ierr",[f,c.now(),!0])}catch(d){}}return"function"==typeof u&&u.apply(this,a(arguments))}function UncaughtException(t,e,n){this.message=t||"Uncaught error with no additional information",this.sourceURL=e,this.line=n}function o(t,e){var n=e?null:c.now();i("err",[t,n])}var i=t("handle"),a=t(32),s=t("ee"),c=t("loader"),f=t("gos"),u=window.onerror,d=!1,p="nr@seenError";if(!c.disabled){var l=0;c.features.err=!0,t(1),window.onerror=r;try{throw new Error}catch(h){"stack"in h&&(t(14),t(13),"addEventListener"in window&&t(7),c.xhrWrappable&&t(15),d=!0)}s.on("fn-start",function(t,e,n){d&&(l+=1)}),s.on("fn-err",function(t,e,n){d&&!n[p]&&(f(n,p,function(){return!0}),this.thrown=!0,o(n))}),s.on("fn-end",function(){d&&!this.thrown&&l>0&&(l-=1)}),s.on("internal-error",function(t){i("ierr",[t,c.now(),!0])})}},{}],3:[function(t,e,n){var r=t("loader");r.disabled||(r.features.ins=!0)},{}],4:[function(t,e,n){function r(){U++,L=g.hash,this[u]=y.now()}function o(){U--,g.hash!==L&&i(0,!0);var t=y.now();this[h]=~~this[h]+t-this[u],this[d]=t}function i(t,e){E.emit("newURL",[""+g,e])}function a(t,e){t.on(e,function(){this[e]=y.now()})}var s="-start",c="-end",f="-body",u="fn"+s,d="fn"+c,p="cb"+s,l="cb"+c,h="jsTime",m="fetch",v="addEventListener",w=window,g=w.location,y=t("loader");if(w[v]&&y.xhrWrappable&&!y.disabled){var x=t(11),b=t(12),E=t(9),R=t(7),O=t(14),T=t(8),S=t(15),P=t(10),M=t("ee"),C=M.get("tracer"),N=t(23);t(17),y.features.spa=!0;var L,U=0;M.on(u,r),b.on(p,r),P.on(p,r),M.on(d,o),b.on(l,o),P.on(l,o),M.buffer([u,d,"xhr-resolved"]),R.buffer([u]),O.buffer(["setTimeout"+c,"clearTimeout"+s,u]),S.buffer([u,"new-xhr","send-xhr"+s]),T.buffer([m+s,m+"-done",m+f+s,m+f+c]),E.buffer(["newURL"]),x.buffer([u]),b.buffer(["propagate",p,l,"executor-err","resolve"+s]),C.buffer([u,"no-"+u]),P.buffer(["new-jsonp","cb-start","jsonp-error","jsonp-end"]),a(T,m+s),a(T,m+"-done"),a(P,"new-jsonp"),a(P,"jsonp-end"),a(P,"cb-start"),E.on("pushState-end",i),E.on("replaceState-end",i),w[v]("hashchange",i,N(!0)),w[v]("load",i,N(!0)),w[v]("popstate",function(){i(0,U>1)},N(!0))}},{}],5:[function(t,e,n){function r(){var t=new PerformanceObserver(function(t,e){var n=t.getEntries();s(v,[n])});try{t.observe({entryTypes:["resource"]})}catch(e){}}function o(t){if(s(v,[window.performance.getEntriesByType(w)]),window.performance["c"+p])try{window.performance[h](m,o,!1)}catch(t){}else try{window.performance[h]("webkit"+m,o,!1)}catch(t){}}function i(t){}if(window.performance&&window.performance.timing&&window.performance.getEntriesByType){var a=t("ee"),s=t("handle"),c=t(14),f=t(13),u=t(6),d=t(23),p="learResourceTimings",l="addEventListener",h="removeEventListener",m="resourcetimingbufferfull",v="bstResource",w="resource",g="-start",y="-end",x="fn"+g,b="fn"+y,E="bstTimer",R="pushState",O=t("loader");if(!O.disabled){O.features.stn=!0,t(9),"addEventListener"in window&&t(7);var T=NREUM.o.EV;a.on(x,function(t,e){var n=t[0];n instanceof T&&(this.bstStart=O.now())}),a.on(b,function(t,e){var n=t[0];n instanceof T&&s("bst",[n,e,this.bstStart,O.now()])}),c.on(x,function(t,e,n){this.bstStart=O.now(),this.bstType=n}),c.on(b,function(t,e){s(E,[e,this.bstStart,O.now(),this.bstType])}),f.on(x,function(){this.bstStart=O.now()}),f.on(b,function(t,e){s(E,[e,this.bstStart,O.now(),"requestAnimationFrame"])}),a.on(R+g,function(t){this.time=O.now(),this.startPath=location.pathname+location.hash}),a.on(R+y,function(t){s("bstHist",[location.pathname+location.hash,this.startPath,this.time])}),u()?(s(v,[window.performance.getEntriesByType("resource")]),r()):l in window.performance&&(window.performance["c"+p]?window.performance[l](m,o,d(!1)):window.performance[l]("webkit"+m,o,d(!1))),document[l]("scroll",i,d(!1)),document[l]("keypress",i,d(!1)),document[l]("click",i,d(!1))}}},{}],6:[function(t,e,n){e.exports=function(){return"PerformanceObserver"in window&&"function"==typeof window.PerformanceObserver}},{}],7:[function(t,e,n){function r(t){for(var e=t;e&&!e.hasOwnProperty(u);)e=Object.getPrototypeOf(e);e&&o(e)}function o(t){s.inPlace(t,[u,d],"-",i)}function i(t,e){return t[1]}var a=t("ee").get("events"),s=t("wrap-function")(a,!0),c=t("gos"),f=XMLHttpRequest,u="addEventListener",d="removeEventListener";e.exports=a,"getPrototypeOf"in Object?(r(document),r(window),r(f.prototype)):f.prototype.hasOwnProperty(u)&&(o(window),o(f.prototype)),a.on(u+"-start",function(t,e){var n=t[1];if(null!==n&&("function"==typeof n||"object"==typeof n)){var r=c(n,"nr@wrapped",function(){function t(){if("function"==typeof n.handleEvent)return n.handleEvent.apply(n,arguments)}var e={object:t,"function":n}[typeof n];return e?s(e,"fn-",null,e.name||"anonymous"):n});this.wrapped=t[1]=r}}),a.on(d+"-start",function(t){t[1]=this.wrapped||t[1]})},{}],8:[function(t,e,n){function r(t,e,n){var r=t[e];"function"==typeof r&&(t[e]=function(){var t=i(arguments),e={};o.emit(n+"before-start",[t],e);var a;e[m]&&e[m].dt&&(a=e[m].dt);var s=r.apply(this,t);return o.emit(n+"start",[t,a],s),s.then(function(t){return o.emit(n+"end",[null,t],s),t},function(t){throw o.emit(n+"end",[t],s),t})})}var o=t("ee").get("fetch"),i=t(32),a=t(31);e.exports=o;var s=window,c="fetch-",f=c+"body-",u=["arrayBuffer","blob","json","text","formData"],d=s.Request,p=s.Response,l=s.fetch,h="prototype",m="nr@context";d&&p&&l&&(a(u,function(t,e){r(d[h],e,f),r(p[h],e,f)}),r(s,"fetch",c),o.on(c+"end",function(t,e){var n=this;if(e){var r=e.headers.get("content-length");null!==r&&(n.rxSize=r),o.emit(c+"done",[null,e],n)}else o.emit(c+"done",[t],n)}))},{}],9:[function(t,e,n){var r=t("ee").get("history"),o=t("wrap-function")(r);e.exports=r;var i=window.history&&window.history.constructor&&window.history.constructor.prototype,a=window.history;i&&i.pushState&&i.replaceState&&(a=i),o.inPlace(a,["pushState","replaceState"],"-")},{}],10:[function(t,e,n){function r(t){function e(){f.emit("jsonp-end",[],l),t.removeEventListener("load",e,c(!1)),t.removeEventListener("error",n,c(!1))}function n(){f.emit("jsonp-error",[],l),f.emit("jsonp-end",[],l),t.removeEventListener("load",e,c(!1)),t.removeEventListener("error",n,c(!1))}var r=t&&"string"==typeof t.nodeName&&"script"===t.nodeName.toLowerCase();if(r){var o="function"==typeof t.addEventListener;if(o){var a=i(t.src);if(a){var d=s(a),p="function"==typeof d.parent[d.key];if(p){var l={};u.inPlace(d.parent,[d.key],"cb-",l),t.addEventListener("load",e,c(!1)),t.addEventListener("error",n,c(!1)),f.emit("new-jsonp",[t.src],l)}}}}}function o(){return"addEventListener"in window}function i(t){var e=t.match(d);return e?e[1]:null}function a(t,e){var n=t.match(l),r=n[1],o=n[3];return o?a(o,e[r]):e[r]}function s(t){var e=t.match(p);return e&&e.length>=3?{key:e[2],parent:a(e[1],window)}:{key:t,parent:window}}var c=t(23),f=t("ee").get("jsonp"),u=t("wrap-function")(f);if(e.exports=f,o()){var d=/[?&](?:callback|cb)=([^&#]+)/,p=/(.*)\.([^.]+)/,l=/^(\w+)(\.|$)(.*)$/,h=["appendChild","insertBefore","replaceChild"];Node&&Node.prototype&&Node.prototype.appendChild?u.inPlace(Node.prototype,h,"dom-"):(u.inPlace(HTMLElement.prototype,h,"dom-"),u.inPlace(HTMLHeadElement.prototype,h,"dom-"),u.inPlace(HTMLBodyElement.prototype,h,"dom-")),f.on("dom-start",function(t){r(t[0])})}},{}],11:[function(t,e,n){var r=t("ee").get("mutation"),o=t("wrap-function")(r),i=NREUM.o.MO;e.exports=r,i&&(window.MutationObserver=function(t){return this instanceof i?new i(o(t,"fn-")):i.apply(this,arguments)},MutationObserver.prototype=i.prototype)},{}],12:[function(t,e,n){function r(t){var e=i.context(),n=s(t,"executor-",e,null,!1),r=new f(n);return i.context(r).getCtx=function(){return e},r}var o=t("wrap-function"),i=t("ee").get("promise"),a=t("ee").getOrSetContext,s=o(i),c=t(31),f=NREUM.o.PR;e.exports=i,f&&(window.Promise=r,["all","race"].forEach(function(t){var e=f[t];f[t]=function(n){function r(t){return function(){i.emit("propagate",[null,!o],a,!1,!1),o=o||!t}}var o=!1;c(n,function(e,n){Promise.resolve(n).then(r("all"===t),r(!1))});var a=e.apply(f,arguments),s=f.resolve(a);return s}}),["resolve","reject"].forEach(function(t){var e=f[t];f[t]=function(t){var n=e.apply(f,arguments);return t!==n&&i.emit("propagate",[t,!0],n,!1,!1),n}}),f.prototype["catch"]=function(t){return this.then(null,t)},f.prototype=Object.create(f.prototype,{constructor:{value:r}}),c(Object.getOwnPropertyNames(f),function(t,e){try{r[e]=f[e]}catch(n){}}),o.wrapInPlace(f.prototype,"then",function(t){return function(){var e=this,n=o.argsToArray.apply(this,arguments),r=a(e);r.promise=e,n[0]=s(n[0],"cb-",r,null,!1),n[1]=s(n[1],"cb-",r,null,!1);var c=t.apply(this,n);return r.nextPromise=c,i.emit("propagate",[e,!0],c,!1,!1),c}}),i.on("executor-start",function(t){t[0]=s(t[0],"resolve-",this,null,!1),t[1]=s(t[1],"resolve-",this,null,!1)}),i.on("executor-err",function(t,e,n){t[1](n)}),i.on("cb-end",function(t,e,n){i.emit("propagate",[n,!0],this.nextPromise,!1,!1)}),i.on("propagate",function(t,e,n){this.getCtx&&!e||(this.getCtx=function(){if(t instanceof Promise)var e=i.context(t);return e&&e.getCtx?e.getCtx():this})}),r.toString=function(){return""+f})},{}],13:[function(t,e,n){var r=t("ee").get("raf"),o=t("wrap-function")(r),i="equestAnimationFrame";e.exports=r,o.inPlace(window,["r"+i,"mozR"+i,"webkitR"+i,"msR"+i],"raf-"),r.on("raf-start",function(t){t[0]=o(t[0],"fn-")})},{}],14:[function(t,e,n){function r(t,e,n){t[0]=a(t[0],"fn-",null,n)}function o(t,e,n){this.method=n,this.timerDuration=isNaN(t[1])?0:+t[1],t[0]=a(t[0],"fn-",this,n)}var i=t("ee").get("timer"),a=t("wrap-function")(i),s="setTimeout",c="setInterval",f="clearTimeout",u="-start",d="-";e.exports=i,a.inPlace(window,[s,"setImmediate"],s+d),a.inPlace(window,[c],c+d),a.inPlace(window,[f,"clearImmediate"],f+d),i.on(c+u,r),i.on(s+u,o)},{}],15:[function(t,e,n){function r(t,e){d.inPlace(e,["onreadystatechange"],"fn-",s)}function o(){var t=this,e=u.context(t);t.readyState>3&&!e.resolved&&(e.resolved=!0,u.emit("xhr-resolved",[],t)),d.inPlace(t,y,"fn-",s)}function i(t){x.push(t),m&&(E?E.then(a):w?w(a):(R=-R,O.data=R))}function a(){for(var t=0;t<x.length;t++)r([],x[t]);x.length&&(x=[])}function s(t,e){return e}function c(t,e){for(var n in t)e[n]=t[n];return e}t(7);var f=t("ee"),u=f.get("xhr"),d=t("wrap-function")(u),p=t(23),l=NREUM.o,h=l.XHR,m=l.MO,v=l.PR,w=l.SI,g="readystatechange",y=["onload","onerror","onabort","onloadstart","onloadend","onprogress","ontimeout"],x=[];e.exports=u;var b=window.XMLHttpRequest=function(t){var e=new h(t);try{u.emit("new-xhr",[e],e),e.addEventListener(g,o,p(!1))}catch(n){try{u.emit("internal-error",[n])}catch(r){}}return e};if(c(h,b),b.prototype=h.prototype,d.inPlace(b.prototype,["open","send"],"-xhr-",s),u.on("send-xhr-start",function(t,e){r(t,e),i(e)}),u.on("open-xhr-start",r),m){var E=v&&v.resolve();if(!w&&!v){var R=1,O=document.createTextNode(R);new m(a).observe(O,{characterData:!0})}}else f.on("fn-end",function(t){t[0]&&t[0].type===g||a()})},{}],16:[function(t,e,n){function r(t){if(!s(t))return null;var e=window.NREUM;if(!e.loader_config)return null;var n=(e.loader_config.accountID||"").toString()||null,r=(e.loader_config.agentID||"").toString()||null,f=(e.loader_config.trustKey||"").toString()||null;if(!n||!r)return null;var h=l.generateSpanId(),m=l.generateTraceId(),v=Date.now(),w={spanId:h,traceId:m,timestamp:v};return(t.sameOrigin||c(t)&&p())&&(w.traceContextParentHeader=o(h,m),w.traceContextStateHeader=i(h,v,n,r,f)),(t.sameOrigin&&!u()||!t.sameOrigin&&c(t)&&d())&&(w.newrelicHeader=a(h,m,v,n,r,f)),w}function o(t,e){return"00-"+e+"-"+t+"-01"}function i(t,e,n,r,o){var i=0,a="",s=1,c="",f="";return o+"@nr="+i+"-"+s+"-"+n+"-"+r+"-"+t+"-"+a+"-"+c+"-"+f+"-"+e}function a(t,e,n,r,o,i){var a="btoa"in window&&"function"==typeof window.btoa;if(!a)return null;var s={v:[0,1],d:{ty:"Browser",ac:r,ap:o,id:t,tr:e,ti:n}};return i&&r!==i&&(s.d.tk=i),btoa(JSON.stringify(s))}function s(t){return f()&&c(t)}function c(t){var e=!1,n={};if("init"in NREUM&&"distributed_tracing"in NREUM.init&&(n=NREUM.init.distributed_tracing),t.sameOrigin)e=!0;else if(n.allowed_origins instanceof Array)for(var r=0;r<n.allowed_origins.length;r++){var o=h(n.allowed_origins[r]);if(t.hostname===o.hostname&&t.protocol===o.protocol&&t.port===o.port){e=!0;break}}return e}function f(){return"init"in NREUM&&"distributed_tracing"in NREUM.init&&!!NREUM.init.distributed_tracing.enabled}function u(){return"init"in NREUM&&"distributed_tracing"in NREUM.init&&!!NREUM.init.distributed_tracing.exclude_newrelic_header}function d(){return"init"in NREUM&&"distributed_tracing"in NREUM.init&&NREUM.init.distributed_tracing.cors_use_newrelic_header!==!1}function p(){return"init"in NREUM&&"distributed_tracing"in NREUM.init&&!!NREUM.init.distributed_tracing.cors_use_tracecontext_headers}var l=t(28),h=t(18);e.exports={generateTracePayload:r,shouldGenerateTrace:s}},{}],17:[function(t,e,n){function r(t){var e=this.params,n=this.metrics;if(!this.ended){this.ended=!0;for(var r=0;r<p;r++)t.removeEventListener(d[r],this.listener,!1);return e.protocol&&"data"===e.protocol?void g("Ajax/DataUrl/Excluded"):void(e.aborted||(n.duration=a.now()-this.startTime,this.loadCaptureCalled||4!==t.readyState?null==e.status&&(e.status=0):i(this,t),n.cbTime=this.cbTime,s("xhr",[e,n,this.startTime,this.endTime,"xhr"],this)))}}function o(t,e){var n=c(e),r=t.params;r.hostname=n.hostname,r.port=n.port,r.protocol=n.protocol,r.host=n.hostname+":"+n.port,r.pathname=n.pathname,t.parsedOrigin=n,t.sameOrigin=n.sameOrigin}function i(t,e){t.params.status=e.status;var n=v(e,t.lastSize);if(n&&(t.metrics.rxSize=n),t.sameOrigin){var r=e.getResponseHeader("X-NewRelic-App-Data");r&&(t.params.cat=r.split(", ").pop())}t.loadCaptureCalled=!0}var a=t("loader");if(a.xhrWrappable&&!a.disabled){var s=t("handle"),c=t(18),f=t(16).generateTracePayload,u=t("ee"),d=["load","error","abort","timeout"],p=d.length,l=t("id"),h=t(24),m=t(22),v=t(19),w=t(23),g=t(25).recordSupportability,y=NREUM.o.REQ,x=window.XMLHttpRequest;a.features.xhr=!0,t(15),t(8),u.on("new-xhr",function(t){var e=this;e.totalCbs=0,e.called=0,e.cbTime=0,e.end=r,e.ended=!1,e.xhrGuids={},e.lastSize=null,e.loadCaptureCalled=!1,e.params=this.params||{},e.metrics=this.metrics||{},t.addEventListener("load",function(n){i(e,t)},w(!1)),h&&(h>34||h<10)||t.addEventListener("progress",function(t){e.lastSize=t.loaded},w(!1))}),u.on("open-xhr-start",function(t){this.params={method:t[0]},o(this,t[1]),this.metrics={}}),u.on("open-xhr-end",function(t,e){"loader_config"in NREUM&&"xpid"in NREUM.loader_config&&this.sameOrigin&&e.setRequestHeader("X-NewRelic-ID",NREUM.loader_config.xpid);var n=f(this.parsedOrigin);if(n){var r=!1;n.newrelicHeader&&(e.setRequestHeader("newrelic",n.newrelicHeader),r=!0),n.traceContextParentHeader&&(e.setRequestHeader("traceparent",n.traceContextParentHeader),n.traceContextStateHeader&&e.setRequestHeader("tracestate",n.traceContextStateHeader),r=!0),r&&(this.dt=n)}}),u.on("send-xhr-start",function(t,e){var n=this.metrics,r=t[0],o=this;if(n&&r){var i=m(r);i&&(n.txSize=i)}this.startTime=a.now(),this.listener=function(t){try{"abort"!==t.type||o.loadCaptureCalled||(o.params.aborted=!0),("load"!==t.type||o.called===o.totalCbs&&(o.onloadCalled||"function"!=typeof e.onload))&&o.end(e)}catch(n){try{u.emit("internal-error",[n])}catch(r){}}};for(var s=0;s<p;s++)e.addEventListener(d[s],this.listener,w(!1))}),u.on("xhr-cb-time",function(t,e,n){this.cbTime+=t,e?this.onloadCalled=!0:this.called+=1,this.called!==this.totalCbs||!this.onloadCalled&&"function"==typeof n.onload||this.end(n)}),u.on("xhr-load-added",function(t,e){var n=""+l(t)+!!e;this.xhrGuids&&!this.xhrGuids[n]&&(this.xhrGuids[n]=!0,this.totalCbs+=1)}),u.on("xhr-load-removed",function(t,e){var n=""+l(t)+!!e;this.xhrGuids&&this.xhrGuids[n]&&(delete this.xhrGuids[n],this.totalCbs-=1)}),u.on("xhr-resolved",function(){this.endTime=a.now()}),u.on("addEventListener-end",function(t,e){e instanceof x&&"load"===t[0]&&u.emit("xhr-load-added",[t[1],t[2]],e)}),u.on("removeEventListener-end",function(t,e){e instanceof x&&"load"===t[0]&&u.emit("xhr-load-removed",[t[1],t[2]],e)}),u.on("fn-start",function(t,e,n){e instanceof x&&("onload"===n&&(this.onload=!0),("load"===(t[0]&&t[0].type)||this.onload)&&(this.xhrCbStart=a.now()))}),u.on("fn-end",function(t,e){this.xhrCbStart&&u.emit("xhr-cb-time",[a.now()-this.xhrCbStart,this.onload,e],e)}),u.on("fetch-before-start",function(t){function e(t,e){var n=!1;return e.newrelicHeader&&(t.set("newrelic",e.newrelicHeader),n=!0),e.traceContextParentHeader&&(t.set("traceparent",e.traceContextParentHeader),e.traceContextStateHeader&&t.set("tracestate",e.traceContextStateHeader),n=!0),n}var n,r=t[1]||{};"string"==typeof t[0]?n=t[0]:t[0]&&t[0].url?n=t[0].url:window.URL&&t[0]&&t[0]instanceof URL&&(n=t[0].href),n&&(this.parsedOrigin=c(n),this.sameOrigin=this.parsedOrigin.sameOrigin);var o=f(this.parsedOrigin);if(o&&(o.newrelicHeader||o.traceContextParentHeader))if("string"==typeof t[0]||window.URL&&t[0]&&t[0]instanceof URL){var i={};for(var a in r)i[a]=r[a];i.headers=new Headers(r.headers||{}),e(i.headers,o)&&(this.dt=o),t.length>1?t[1]=i:t.push(i)}else t[0]&&t[0].headers&&e(t[0].headers,o)&&(this.dt=o)}),u.on("fetch-start",function(t,e){this.params={},this.metrics={},this.startTime=a.now(),this.dt=e,t.length>=1&&(this.target=t[0]),t.length>=2&&(this.opts=t[1]);var n,r=this.opts||{},i=this.target;if("string"==typeof i?n=i:"object"==typeof i&&i instanceof y?n=i.url:window.URL&&"object"==typeof i&&i instanceof URL&&(n=i.href),o(this,n),"data"!==this.params.protocol){var s=(""+(i&&i instanceof y&&i.method||r.method||"GET")).toUpperCase();this.params.method=s,this.txSize=m(r.body)||0}}),u.on("fetch-done",function(t,e){if(this.endTime=a.now(),this.params||(this.params={}),"data"===this.params.protocol)return void g("Ajax/DataUrl/Excluded");this.params.status=e?e.status:0;var n;"string"==typeof this.rxSize&&this.rxSize.length>0&&(n=+this.rxSize);var r={txSize:this.txSize,rxSize:n,duration:a.now()-this.startTime};s("xhr",[this.params,r,this.startTime,this.endTime,"fetch"],this)})}},{}],18:[function(t,e,n){var r={};e.exports=function(t){if(t in r)return r[t];if(0===(t||"").indexOf("data:"))return{protocol:"data"};var e=document.createElement("a"),n=window.location,o={};e.href=t,o.port=e.port;var i=e.href.split("://");!o.port&&i[1]&&(o.port=i[1].split("/")[0].split("@").pop().split(":")[1]),o.port&&"0"!==o.port||(o.port="https"===i[0]?"443":"80"),o.hostname=e.hostname||n.hostname,o.pathname=e.pathname,o.protocol=i[0],"/"!==o.pathname.charAt(0)&&(o.pathname="/"+o.pathname);var a=!e.protocol||":"===e.protocol||e.protocol===n.protocol,s=e.hostname===document.domain&&e.port===n.port;return o.sameOrigin=a&&(!e.hostname||s),"/"===o.pathname&&(r[t]=o),o}},{}],19:[function(t,e,n){function r(t,e){var n=t.responseType;return"json"===n&&null!==e?e:"arraybuffer"===n||"blob"===n||"json"===n?o(t.response):"text"===n||""===n||void 0===n?o(t.responseText):void 0}var o=t(22);e.exports=r},{}],20:[function(t,e,n){function r(){}function o(t,e,n,r){return function(){return u.recordSupportability("API/"+e+"/called"),i(t+e,[f.now()].concat(s(arguments)),n?null:this,r),n?void 0:this}}var i=t("handle"),a=t(31),s=t(32),c=t("ee").get("tracer"),f=t("loader"),u=t(25),d=NREUM;"undefined"==typeof window.newrelic&&(newrelic=d);var p=["setPageViewName","setCustomAttribute","setErrorHandler","finished","addToTrace","inlineHit","addRelease"],l="api-",h=l+"ixn-";a(p,function(t,e){d[e]=o(l,e,!0,"api")}),d.addPageAction=o(l,"addPageAction",!0),d.setCurrentRouteName=o(l,"routeName",!0),e.exports=newrelic,d.interaction=function(){return(new r).get()};var m=r.prototype={createTracer:function(t,e){var n={},r=this,o="function"==typeof e;return i(h+"tracer",[f.now(),t,n],r),function(){if(c.emit((o?"":"no-")+"fn-start",[f.now(),r,o],n),o)try{return e.apply(this,arguments)}catch(t){throw c.emit("fn-err",[arguments,this,t],n),t}finally{c.emit("fn-end",[f.now()],n)}}}};a("actionText,setName,setAttribute,save,ignore,onEnd,getContext,end,get".split(","),function(t,e){m[e]=o(h,e)}),newrelic.noticeError=function(t,e){"string"==typeof t&&(t=new Error(t)),u.recordSupportability("API/noticeError/called"),i("err",[t,f.now(),!1,e])}},{}],21:[function(t,e,n){function r(t){if(NREUM.init){for(var e=NREUM.init,n=t.split("."),r=0;r<n.length-1;r++)if(e=e[n[r]],"object"!=typeof e)return;return e=e[n[n.length-1]]}}e.exports={getConfiguration:r}},{}],22:[function(t,e,n){e.exports=function(t){if("string"==typeof t&&t.length)return t.length;if("object"==typeof t){if("undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer&&t.byteLength)return t.byteLength;if("undefined"!=typeof Blob&&t instanceof Blob&&t.size)return t.size;if(!("undefined"!=typeof FormData&&t instanceof FormData))try{return JSON.stringify(t).length}catch(e){return}}}},{}],23:[function(t,e,n){var r=!1;try{var o=Object.defineProperty({},"passive",{get:function(){r=!0}});window.addEventListener("testPassive",null,o),window.removeEventListener("testPassive",null,o)}catch(i){}e.exports=function(t){return r?{passive:!0,capture:!!t}:!!t}},{}],24:[function(t,e,n){var r=0,o=navigator.userAgent.match(/Firefox[\/\s](\d+\.\d+)/);o&&(r=+o[1]),e.exports=r},{}],25:[function(t,e,n){function r(t,e){var n=[a,t,{name:t},e];return i("storeMetric",n,null,"api"),n}function o(t,e){var n=[s,t,{name:t},e];return i("storeEventMetrics",n,null,"api"),n}var i=t("handle"),a="sm",s="cm";e.exports={constants:{SUPPORTABILITY_METRIC:a,CUSTOM_METRIC:s},recordSupportability:r,recordCustom:o}},{}],26:[function(t,e,n){function r(){return s.exists&&performance.now?Math.round(performance.now()):(i=Math.max((new Date).getTime(),i))-a}function o(){return i}var i=(new Date).getTime(),a=i,s=t(33);e.exports=r,e.exports.offset=a,e.exports.getLastTimestamp=o},{}],27:[function(t,e,n){function r(t,e){var n=t.getEntries();n.forEach(function(t){"first-paint"===t.name?l("timing",["fp",Math.floor(t.startTime)]):"first-contentful-paint"===t.name&&l("timing",["fcp",Math.floor(t.startTime)])})}function o(t,e){var n=t.getEntries();if(n.length>0){var r=n[n.length-1];if(f&&f<r.startTime)return;var o=[r],i=a({});i&&o.push(i),l("lcp",o)}}function i(t){t.getEntries().forEach(function(t){t.hadRecentInput||l("cls",[t])})}function a(t){var e=navigator.connection||navigator.mozConnection||navigator.webkitConnection;if(e)return e.type&&(t["net-type"]=e.type),e.effectiveType&&(t["net-etype"]=e.effectiveType),e.rtt&&(t["net-rtt"]=e.rtt),e.downlink&&(t["net-dlink"]=e.downlink),t}function s(t){if(t instanceof w&&!y){var e=Math.round(t.timeStamp),n={type:t.type};a(n),e<=h.now()?n.fid=h.now()-e:e>h.offset&&e<=Date.now()?(e-=h.offset,n.fid=h.now()-e):e=h.now(),y=!0,l("timing",["fi",e,n])}}function c(t){"hidden"===t&&(f=h.now(),l("pageHide",[f]))}if(!("init"in NREUM&&"page_view_timing"in NREUM.init&&"enabled"in NREUM.init.page_view_timing&&NREUM.init.page_view_timing.enabled===!1)){var f,u,d,p,l=t("handle"),h=t("loader"),m=t(30),v=t(23),w=NREUM.o.EV;if("PerformanceObserver"in window&&"function"==typeof window.PerformanceObserver){u=new PerformanceObserver(r);try{u.observe({entryTypes:["paint"]})}catch(g){}d=new PerformanceObserver(o);try{d.observe({entryTypes:["largest-contentful-paint"]})}catch(g){}p=new PerformanceObserver(i);try{p.observe({type:"layout-shift",buffered:!0})}catch(g){}}if("addEventListener"in document){var y=!1,x=["click","keydown","mousedown","pointerdown","touchstart"];x.forEach(function(t){document.addEventListener(t,s,v(!1))})}m(c)}},{}],28:[function(t,e,n){function r(){function t(){return e?15&e[n++]:16*Math.random()|0}var e=null,n=0,r=window.crypto||window.msCrypto;r&&r.getRandomValues&&(e=r.getRandomValues(new Uint8Array(31)));for(var o,i="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx",a="",s=0;s<i.length;s++)o=i[s],"x"===o?a+=t().toString(16):"y"===o?(o=3&t()|8,a+=o.toString(16)):a+=o;return a}function o(){return a(16)}function i(){return a(32)}function a(t){function e(){return n?15&n[r++]:16*Math.random()|0}var n=null,r=0,o=window.crypto||window.msCrypto;o&&o.getRandomValues&&Uint8Array&&(n=o.getRandomValues(new Uint8Array(t)));for(var i=[],a=0;a<t;a++)i.push(e().toString(16));return i.join("")}e.exports={generateUuid:r,generateSpanId:o,generateTraceId:i}},{}],29:[function(t,e,n){function r(t,e){if(!o)return!1;if(t!==o)return!1;if(!e)return!0;if(!i)return!1;for(var n=i.split("."),r=e.split("."),a=0;a<r.length;a++)if(r[a]!==n[a])return!1;return!0}var o=null,i=null,a=/Version\/(\S+)\s+Safari/;if(navigator.userAgent){var s=navigator.userAgent,c=s.match(a);c&&s.indexOf("Chrome")===-1&&s.indexOf("Chromium")===-1&&(o="Safari",i=c[1])}e.exports={agent:o,version:i,match:r}},{}],30:[function(t,e,n){function r(t){function e(){t(s&&document[s]?document[s]:document[i]?"hidden":"visible")}"addEventListener"in document&&a&&document.addEventListener(a,e,o(!1))}var o=t(23);e.exports=r;var i,a,s;"undefined"!=typeof document.hidden?(i="hidden",a="visibilitychange",s="visibilityState"):"undefined"!=typeof document.msHidden?(i="msHidden",a="msvisibilitychange"):"undefined"!=typeof document.webkitHidden&&(i="webkitHidden",a="webkitvisibilitychange",s="webkitVisibilityState")},{}],31:[function(t,e,n){function r(t,e){var n=[],r="",i=0;for(r in t)o.call(t,r)&&(n[i]=e(r,t[r]),i+=1);return n}var o=Object.prototype.hasOwnProperty;e.exports=r},{}],32:[function(t,e,n){function r(t,e,n){e||(e=0),"undefined"==typeof n&&(n=t?t.length:0);for(var r=-1,o=n-e||0,i=Array(o<0?0:o);++r<o;)i[r]=t[e+r];return i}e.exports=r},{}],33:[function(t,e,n){e.exports={exists:"undefined"!=typeof window.performance&&window.performance.timing&&"undefined"!=typeof window.performance.timing.navigationStart}},{}],ee:[function(t,e,n){function r(){}function o(t){function e(t){return t&&t instanceof r?t:t?f(t,c,a):a()}function n(n,r,o,i,a){if(a!==!1&&(a=!0),!l.aborted||i){t&&a&&t(n,r,o);for(var s=e(o),c=m(n),f=c.length,u=0;u<f;u++)c[u].apply(s,r);var p=d[y[n]];return p&&p.push([x,n,r,s]),s}}function i(t,e){g[t]=m(t).concat(e)}function h(t,e){var n=g[t];if(n)for(var r=0;r<n.length;r++)n[r]===e&&n.splice(r,1)}function m(t){return g[t]||[]}function v(t){return p[t]=p[t]||o(n)}function w(t,e){l.aborted||u(t,function(t,n){e=e||"feature",y[n]=e,e in d||(d[e]=[])})}var g={},y={},x={on:i,addEventListener:i,removeEventListener:h,emit:n,get:v,listeners:m,context:e,buffer:w,abort:s,aborted:!1};return x}function i(t){return f(t,c,a)}function a(){return new r}function s(){(d.api||d.feature)&&(l.aborted=!0,d=l.backlog={})}var c="nr@context",f=t("gos"),u=t(31),d={},p={},l=e.exports=o();e.exports.getOrSetContext=i,l.backlog=d},{}],gos:[function(t,e,n){function r(t,e,n){if(o.call(t,e))return t[e];var r=n();if(Object.defineProperty&&Object.keys)try{return Object.defineProperty(t,e,{value:r,writable:!0,enumerable:!1}),r}catch(i){}return t[e]=r,r}var o=Object.prototype.hasOwnProperty;e.exports=r},{}],handle:[function(t,e,n){function r(t,e,n,r){o.buffer([t],r),o.emit(t,e,n)}var o=t("ee").get("handle");e.exports=r,r.ee=o},{}],id:[function(t,e,n){function r(t){var e=typeof t;return!t||"object"!==e&&"function"!==e?-1:t===window?0:a(t,i,function(){return o++})}var o=1,i="nr@id",a=t("gos");e.exports=r},{}],loader:[function(t,e,n){function r(){if(!T++){var t=O.info=NREUM.info,e=m.getElementsByTagName("script")[0];if(setTimeout(f.abort,3e4),!(t&&t.licenseKey&&t.applicationID&&e))return f.abort();c(E,function(e,n){t[e]||(t[e]=n)});var n=a();s("mark",["onload",n+O.offset],null,"api"),s("timing",["load",n]);var r=m.createElement("script");0===t.agent.indexOf("http://")||0===t.agent.indexOf("https://")?r.src=t.agent:r.src=l+"://"+t.agent,e.parentNode.insertBefore(r,e)}}function o(){"complete"===m.readyState&&i()}function i(){s("mark",["domContent",a()+O.offset],null,"api")}var a=t(26),s=t("handle"),c=t(31),f=t("ee"),u=t(29),d=t(21),p=t(23),l=d.getConfiguration("ssl")===!1?"http":"https",h=window,m=h.document,v="addEventListener",w="attachEvent",g=h.XMLHttpRequest,y=g&&g.prototype,x=!1;NREUM.o={ST:setTimeout,SI:h.setImmediate,CT:clearTimeout,XHR:g,REQ:h.Request,EV:h.Event,PR:h.Promise,MO:h.MutationObserver};var b=""+location,E={beacon:"bam.nr-data.net",errorBeacon:"bam.nr-data.net",agent:"js-agent.newrelic.com/nr-spa-1216.min.js"},R=g&&y&&y[v]&&!/CriOS/.test(navigator.userAgent),O=e.exports={offset:a.getLastTimestamp(),now:a,origin:b,features:{},xhrWrappable:R,userAgent:u,disabled:x};if(!x){t(20),t(27),m[v]?(m[v]("DOMContentLoaded",i,p(!1)),h[v]("load",r,p(!1))):(m[w]("onreadystatechange",o),h[w]("onload",r)),s("mark",["firstbyte",a.getLastTimestamp()],null,"api");var T=0}},{}],"wrap-function":[function(t,e,n){function r(t,e){function n(e,n,r,c,f){function nrWrapper(){var i,a,u,p;try{a=this,i=d(arguments),u="function"==typeof r?r(i,a):r||{}}catch(l){o([l,"",[i,a,c],u],t)}s(n+"start",[i,a,c],u,f);try{return p=e.apply(a,i)}catch(h){throw s(n+"err",[i,a,h],u,f),h}finally{s(n+"end",[i,a,p],u,f)}}return a(e)?e:(n||(n=""),nrWrapper[p]=e,i(e,nrWrapper,t),nrWrapper)}function r(t,e,r,o,i){r||(r="");var s,c,f,u="-"===r.charAt(0);for(f=0;f<e.length;f++)c=e[f],s=t[c],a(s)||(t[c]=n(s,u?c+r:r,o,c,i))}function s(n,r,i,a){if(!h||e){var s=h;h=!0;try{t.emit(n,r,i,e,a)}catch(c){o([c,n,r,i],t)}h=s}}return t||(t=u),n.inPlace=r,n.flag=p,n}function o(t,e){e||(e=u);try{e.emit("internal-error",t)}catch(n){}}function i(t,e,n){if(Object.defineProperty&&Object.keys)try{var r=Object.keys(t);return r.forEach(function(n){Object.defineProperty(e,n,{get:function(){return t[n]},set:function(e){return t[n]=e,e}})}),e}catch(i){o([i],n)}for(var a in t)l.call(t,a)&&(e[a]=t[a]);return e}function a(t){return!(t&&t instanceof Function&&t.apply&&!t[p])}function s(t,e){var n=e(t);return n[p]=t,i(t,n,u),n}function c(t,e,n){var r=t[e];t[e]=s(r,n)}function f(){for(var t=arguments.length,e=new Array(t),n=0;n<t;++n)e[n]=arguments[n];return e}var u=t("ee"),d=t(32),p="nr@original",l=Object.prototype.hasOwnProperty,h=!1;e.exports=r,e.exports.wrapFunction=s,e.exports.wrapInPlace=c,e.exports.argsToArray=f},{}]},{},["loader",2,17,5,3,4]);</script> <script defer="defer" src="./Kotak Neo Trade API 1.0.0_files/messenger-setup.js.download" nonce=""></script><script id="_pmPostmanRunObject" async="" src="./Kotak Neo Trade API 1.0.0_files/button.js.download"></script><style data-styled="active" data-styled-version="5.3.6"></style><style data-styled="active" data-styled-version="5.1.1"></style><style data-emotion="css" data-s=""></style></head><body class="" style="overflow: hidden;"><style id="postman-critical-css" type="text/css">.postman-run-button{background:url('data:image/svg+xml;base64,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');display:inline-block;border-radius:4px;cursor:pointer;height:32px;border:none;width:123px}.postman-run-button:hover{background:url('data:image/svg+xml;base64,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')}</style><div id="aether-tab-portal" style="display: inline-block; position: absolute; visibility: hidden; z-index: -1; height: 0px; overflow: hidden;"><ul class="TabList__StyledTabList-xqq1cg-0 ipIhZz" type="primary"><li type="primary" class="Tab__StyledPrimaryTabWrapper-mv7d6n-3 csIoCK"><button class="Tab__StyledTab-mv7d6n-0 cAEscj"><span type="" color="" class="Text__TextContainer-sc-1kqigik-0 dLPxIq" data-aether-id="aether-text" data-testid="aether-text" data-click="">Body</span></button></li><li type="primary" class="Tab__StyledPrimaryTabWrapper-mv7d6n-3 eodlZY"><button class="Tab__StyledTab-mv7d6n-0 bsbHut"><span type="" color="" class="Text__TextContainer-sc-1kqigik-0 dLPxIq" data-aether-id="aether-text" data-testid="aether-text" data-click="">Headers (30)</span></button></li></ul></div><div id="aether-dropdown-portal"></div><div id="aether-toast-notifications__container"></div><script defer="defer" src="./Kotak Neo Trade API 1.0.0_files/production.min.ad05926692a839a46ac1.js.download" nonce="" id="script-data-scope" data-var-user-id="" data-var-environment="production" data-var-team-id="" data-var-host="" data-var-hostname="documenter.getpostman.com" data-var-api-host="https://documenter.gw.postman.com" data-var-documenter-domains-regex="^documenter.(postman|getpostman).com$" data-var-version="1.35.11" data-var-sentry-dsn="https://<EMAIL>/4504247918526464"></script><script defer="defer" src="./Kotak Neo Trade API 1.0.0_files/main.79b4f809a26ea6c20844.js.download" nonce=""></script> <script src="./Kotak Neo Trade API 1.0.0_files/raven.min.js.download" nonce="" crossorigin="anonymous"></script><script nonce="">Raven.config("https://<EMAIL>/4504247918526464").install();</script><script defer="defer" src="./Kotak Neo Trade API 1.0.0_files/runbutton.js.download" nonce="" id="public-run-button-embed" data-web-host="https://www.getpostman.com/" data-button-url="https://run.pstmn.io/button.js"></script><script id="pm-rip" src="./Kotak Neo Trade API 1.0.0_files/pm-rip.js.download" nonce="" data-url="https://analytics.getpostman.com" data-collection-id="1395ba24-126c-4b7f-859b-0ba5934fc416"></script><div id="root"><div><div class="layoutstyles__LayoutContainer-sc-sfl92e-0 kVvNVE"><div class="layoutstyles__FixedContainer-sc-sfl92e-1 jbqrgf layoutFixedContainer"><div data-testid="topbar" class="appbarstyles__AppbarContainer-sc-18djs7m-3 KOMLi"><div class="appbarstyles__AppbarLeft-sc-18djs7m-1 jgvkFD"><div data-testid="menuToggle" class="styles__MenuToggle-sc-1xpix1f-10 bytSus"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M14 3H2V2H14V3Z" fill="#666666"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M14 8.5H2V7.5H14V8.5Z" fill="#666666"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M14 14H2V13H14V14Z" fill="#666666"></path></svg></div><div data-testid="topbar-logo" class="appbarstyles__Logo-sc-18djs7m-0 havaYG"></div></div><div class="appbarstyles__AppbarRight-sc-18djs7m-2 dqRePj"><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 eZdXjo  " data-testid="topbar-public-label" data-click=""><div class="styles__Public-sc-1xpix1f-4 iMppsS"><div class="styles__GlobeIcon-sc-1xpix1f-0 dUHuxy"></div><span class="styles__PublicText-sc-1xpix1f-5 gZZLzk"> Public </span></div><div class="styles__OnlyGlobeIcon-sc-1xpix1f-2 fsIion"><div class="styles__GlobeIcon-sc-1xpix1f-0 dUHuxy"></div></div></div><div class="rip-visible postman-run-button" data-testid="postman-run-button" data-postman-action="collection/import" data-postman-var-1="21534797-1395ba24-126c-4b7f-859b-0ba5934fc416-UzBnqmpD" data-postman-var-2="latest" data-postman-button-index="0" data-postman-source="documenter"></div><div data-testid="settingToggle" class="styles__SettingToggle-sc-1xpix1f-8 VaLAx"><span type="" color="" class="Text__TextContainer-sc-1kqigik-0 dLPxIq" data-aether-id="aether-text" data-testid="aether-text" data-click="">Documentation Settings</span><i class="IconWrapper__IconContainer-gnjn48-0 dkgnYV" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.00004 9.29294L4.35359 5.64649L3.64648 6.3536L8.00004 10.7072L12.3536 6.3536L11.6465 5.64649L8.00004 9.29294Z" fill="#6B6B6B"></path></svg></i></div></div></div></div><div id="sticky-config-bar-checker"></div><div class="layoutstyles__StickyConfigbarContainer-sc-sfl92e-2 cNIANw"><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 blIsiO  " data-testid="aether-flex" data-click=""><div id="config-bar" data-testid="configbar" class="config-barstyles__ConfigBarContainer-sc-5plgy2-0 CWDmH"><div data-aether-id="aether-dropdown" class="DropdownContainer__StyledDropdownContainer-bmd2h5-0 dFENEc"><div class="DropdownLabel__StyledDropdownInfo-hji92t-0 hSWKUV"><label for="" type="primary" data-aether-id="aether-label" data-testid="aether-label" data-click="" class="Label__LabelElement-sc-14v3am6-1 eKSWfX">ENVIRONMENT</label></div><div class="SingleSelect__StyledReactSelect-sc-1dz28nt-0 kHlJqB configbar-dropdown css-nxiuxh-container"><span id="react-select-2-live-region" class="css-7pg0cj-a11yText"></span><span aria-live="polite" aria-atomic="false" aria-relevant="additions text" class="css-7pg0cj-a11yText"></span><div class="aether-dropdown__control css-1kvysec-control"><div class="aether-dropdown__value-container aether-dropdown__value-container--has-value css-1s8hhp9"><div class="aether-dropdown__single-value css-15leyl8-singleValue">No Environment</div><input id="react-select-2-input" tabindex="0" inputmode="none" aria-autocomplete="list" aria-expanded="false" aria-haspopup="true" aria-controls="react-select-2-listbox" aria-owns="react-select-2-listbox" role="combobox" aria-readonly="true" class="css-1hac4vs-dummyInput" value=""></div><div class="aether-dropdown__indicators css-1wy0on6"><div class="aether-dropdown__indicator aether-dropdown__dropdown-indicator css-tlfecz-indicatorContainer" aria-hidden="true"><i class="IconWrapper__IconContainer-gnjn48-0 hImIRl" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.00004 9.29294L4.35359 5.64649L3.64648 6.3536L8.00004 10.7072L12.3536 6.3536L11.6465 5.64649L8.00004 9.29294Z" fill="#6B6B6B"></path></svg></i></div></div></div></div></div><div data-aether-id="aether-dropdown" class="DropdownContainer__StyledDropdownContainer-bmd2h5-0 dFENEc"><div class="DropdownLabel__StyledDropdownInfo-hji92t-0 hSWKUV"><label for="" type="primary" data-aether-id="aether-label" data-testid="aether-label" data-click="" class="Label__LabelElement-sc-14v3am6-1 eKSWfX">LAYOUT</label></div><div class="SingleSelect__StyledReactSelect-sc-1dz28nt-0 kHlJqB configbar-dropdown css-nxiuxh-container"><span id="react-select-3-live-region" class="css-7pg0cj-a11yText"></span><span aria-live="polite" aria-atomic="false" aria-relevant="additions text" class="css-7pg0cj-a11yText"></span><div class="aether-dropdown__control css-1kvysec-control"><div class="aether-dropdown__value-container aether-dropdown__value-container--has-value css-1s8hhp9"><div class="aether-dropdown__single-value css-15leyl8-singleValue">Double Column</div><input id="react-select-3-input" tabindex="0" inputmode="none" aria-autocomplete="list" aria-expanded="false" aria-haspopup="true" aria-controls="react-select-3-listbox" aria-owns="react-select-3-listbox" role="combobox" aria-readonly="true" class="css-1hac4vs-dummyInput" value=""></div><div class="aether-dropdown__indicators css-1wy0on6"><div class="aether-dropdown__indicator aether-dropdown__dropdown-indicator css-tlfecz-indicatorContainer" aria-hidden="true"><i class="IconWrapper__IconContainer-gnjn48-0 hImIRl" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.00004 9.29294L4.35359 5.64649L3.64648 6.3536L8.00004 10.7072L12.3536 6.3536L11.6465 5.64649L8.00004 9.29294Z" fill="#6B6B6B"></path></svg></i></div></div></div></div></div><div data-aether-id="aether-dropdown" class="DropdownContainer__StyledDropdownContainer-bmd2h5-0 dFENEc"><div class="DropdownLabel__StyledDropdownInfo-hji92t-0 hSWKUV"><label for="" type="primary" data-aether-id="aether-label" data-testid="aether-label" data-click="" class="Label__LabelElement-sc-14v3am6-1 eKSWfX">LANGUAGE</label></div><div class="SingleSelect__StyledReactSelect-sc-1dz28nt-0 kHlJqB configbar-dropdown css-nxiuxh-container"><span id="react-select-4-live-region" class="css-7pg0cj-a11yText"></span><span aria-live="polite" aria-atomic="false" aria-relevant="additions text" class="css-7pg0cj-a11yText"></span><div class="aether-dropdown__control css-1kvysec-control"><div class="aether-dropdown__value-container aether-dropdown__value-container--has-value css-1s8hhp9"><div class="aether-dropdown__single-value css-15leyl8-singleValue">Dart - http</div><input id="react-select-4-input" tabindex="0" inputmode="none" aria-autocomplete="list" aria-expanded="false" aria-haspopup="true" aria-controls="react-select-4-listbox" aria-owns="react-select-4-listbox" role="combobox" aria-readonly="true" class="css-1hac4vs-dummyInput" value=""></div><div class="aether-dropdown__indicators css-1wy0on6"><div class="aether-dropdown__indicator aether-dropdown__dropdown-indicator css-tlfecz-indicatorContainer" aria-hidden="true"><i class="IconWrapper__IconContainer-gnjn48-0 hImIRl" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.00004 9.29294L4.35359 5.64649L3.64648 6.3536L8.00004 10.7072L12.3536 6.3536L11.6465 5.64649L8.00004 9.29294Z" fill="#6B6B6B"></path></svg></i></div></div></div></div></div><div class="settings-modalstyles__SettingsModalContainer-sc-hjw122-0 dHUWqm"><button type="tertiary" class="Button__StyledButton-b8af3e-1 klCLOJ" data-aether-id="aether-button" data-testid="aether-button" data-click=""><i class="IconWrapper__IconContainer-gnjn48-0 dkgnYV aether-button__left-icon" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M6.84137 3.162L6.30056 3.34946C5.73347 3.54604 5.21031 3.83854 4.75067 4.20815L4.32319 4.5519L2.30451 3.86901L1.57593 5.13095L3.09017 6.46171L2.95043 7.04743C2.85799 7.43487 2.80881 7.84007 2.80881 8.25806C2.80881 8.54577 2.83211 8.8273 2.87672 9.10107L2.96579 9.64756L1.57592 10.869L2.3045 12.131L3.95735 11.5718L4.39549 11.9948C4.95617 12.5362 5.63721 12.9517 6.39322 13.1978L6.94955 13.3789L7.27377 15H8.73094L9.05516 13.3789L9.6115 13.1978C10.3675 12.9517 11.0486 12.5362 11.6092 11.9948L12.0474 11.5718L13.7002 12.131L14.4288 10.869L13.0389 9.64754L13.128 9.10105C13.1726 8.82729 13.1959 8.54576 13.1959 8.25806C13.1959 7.84008 13.1467 7.43489 13.0543 7.04745L12.9145 6.46173L14.4288 5.13097L13.7002 3.86902L11.6815 4.55192L11.2541 4.20816C10.7944 3.83855 10.2713 3.54604 9.70415 3.34946L9.16334 3.162L8.73094 1H7.27377L6.84137 3.162ZM9.55074 0H6.45397L5.97305 2.40462C5.29562 2.63944 4.67159 2.98854 4.12402 3.42886L1.84839 2.65904L0.300002 5.34092L1.97773 6.81536C1.86728 7.27826 1.80881 7.76133 1.80881 8.25806C1.80881 8.59986 1.83649 8.93519 1.88975 9.26193L0.299988 10.6591L1.84837 13.3409L3.7009 12.7143C4.36849 13.3588 5.18053 13.8547 6.08371 14.1487L6.45397 16H9.55074L9.921 14.1487C10.8242 13.8547 11.6362 13.3588 12.3038 12.7142L14.1564 13.3409L15.7047 10.659L14.115 9.2619C14.1682 8.93517 14.1959 8.59985 14.1959 8.25806C14.1959 7.76134 14.1374 7.27828 14.027 6.81538L15.7047 5.34094L14.1563 2.65905L11.8807 3.42887C11.3331 2.98855 10.7091 2.63944 10.0317 2.40462L9.55074 0ZM11.49 8.25995C11.49 10.1929 9.92297 11.76 7.98997 11.76C6.05697 11.76 4.48997 10.1929 4.48997 8.25995C4.48997 6.32695 6.05697 4.75995 7.98997 4.75995C9.92297 4.75995 11.49 6.32695 11.49 8.25995ZM7.98997 10.76C9.37068 10.76 10.49 9.64066 10.49 8.25995C10.49 6.87924 9.37068 5.75995 7.98997 5.75995C6.60926 5.75995 5.48997 6.87924 5.48997 8.25995C5.48997 9.64066 6.60926 10.76 7.98997 10.76Z" fill="#6B6B6B"></path></svg></i></button></div><span class="config-barstyles__ThemeToggleContainer-sc-5plgy2-1 gXamAz"><div class="rip-visible postman-run-button" data-testid="postman-run-button" data-postman-action="collection/import" data-postman-var-1="21534797-1395ba24-126c-4b7f-859b-0ba5934fc416-UzBnqmpD" data-postman-var-2="latest" data-postman-button-index="1" data-postman-source="documenter"></div></span></div></div></div><div class="layoutstyles__ContentContainer-sc-sfl92e-3 jFANMe"><div class="layoutstyles__FixedWidthContainer-sc-sfl92e-4 hlarVr"><div class="sc-fzoxnE navbarstyles__NavbarContainer-sc-4bjpr8-0 GqMIW fRIgmg"><div class="sc-fzqzEs eVQcHf"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 lpqKuc" data-aether-id="aether-text" data-testid="aether-text" data-click="">Kotak Neo Trade API 1.0.0</span></div><div class="sc-fzqOul WXIAt"><a href="https://documenter.getpostman.com/view/21534797/UzBnqmpD#intro">Introduction </a></div><div class="sc-fzoMdx fYHVvJ documentation-core-list__item-icon"><div class="sc-fznzqM dYlvIp core-icon-button"><i color="content-color-primary" class="IconWrapper__IconContainer-gnjn48-0 kmurjI" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.00004 9.29294L4.35359 5.64649L3.64648 6.3536L8.00004 10.7072L12.3536 6.3536L11.6465 5.64649L8.00004 9.29294Z" fill="#6B6B6B"></path></svg></i></div><div color="9c274c" class="sc-fznOgF hWmdUF"><i color="content-color-secondary" class="IconWrapper__IconContainer-gnjn48-0 LzfYz" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.5 1C1.67157 1 1 1.67157 1 2.5V13.5C1 14.3284 1.67157 15 2.5 15H13.5C14.3284 15 15 14.3284 15 13.5V4.5C15 3.67157 14.3284 3 13.5 3H8.70711L7.14645 1.43934C6.86514 1.15803 6.48361 1 6.08579 1H2.5ZM2 2.5C2 2.22386 2.22386 2 2.5 2H6.08579C6.21839 2 6.34557 2.05268 6.43934 2.14645L8.14645 3.85355C8.24021 3.94732 8.36739 4 8.5 4H13.5C13.7761 4 14 4.22386 14 4.5V6H2V2.5ZM2 7V13.5C2 13.7761 2.22386 14 2.5 14H13.5C13.7761 14 14 13.7761 14 13.5V7H2Z" fill="#6B6B6B"></path></svg></i></div><div class="sc-fznYue kfdnyY documentation-core-list__item-name" style="text-decoration: initial;">Generate Access Token</div></div><div class="core-children-container" style="padding-left: 12px;"><div class="sc-fzoWqW gqsYtg"><div class="sc-fzplgP jPtEHd">POST</div><div class="sc-fzonjX jMaMuX documentation-core-list__item-name">Access Token</div></div></div><div class="sc-fzoMdx fYHVvJ documentation-core-list__item-icon"><div class="sc-fznzqM dYlvIp core-icon-button"><i color="content-color-primary" class="IconWrapper__IconContainer-gnjn48-0 kmurjI" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.00004 9.29294L4.35359 5.64649L3.64648 6.3536L8.00004 10.7072L12.3536 6.3536L11.6465 5.64649L8.00004 9.29294Z" fill="#6B6B6B"></path></svg></i></div><div color="9c274c" class="sc-fznOgF hWmdUF"><i color="content-color-secondary" class="IconWrapper__IconContainer-gnjn48-0 LzfYz" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.5 1C1.67157 1 1 1.67157 1 2.5V13.5C1 14.3284 1.67157 15 2.5 15H13.5C14.3284 15 15 14.3284 15 13.5V4.5C15 3.67157 14.3284 3 13.5 3H8.70711L7.14645 1.43934C6.86514 1.15803 6.48361 1 6.08579 1H2.5ZM2 2.5C2 2.22386 2.22386 2 2.5 2H6.08579C6.21839 2 6.34557 2.05268 6.43934 2.14645L8.14645 3.85355C8.24021 3.94732 8.36739 4 8.5 4H13.5C13.7761 4 14 4.22386 14 4.5V6H2V2.5ZM2 7V13.5C2 13.7761 2.22386 14 2.5 14H13.5C13.7761 14 14 13.7761 14 13.5V7H2Z" fill="#6B6B6B"></path></svg></i></div><div class="sc-fznYue kfdnyY documentation-core-list__item-name" style="text-decoration: initial;">Login with TOTP</div></div><div class="core-children-container" style="padding-left: 12px;"><div class="sc-fzoWqW gqsYtg"><div class="sc-fzplgP jPtEHd">POST</div><div class="sc-fzonjX jMaMuX documentation-core-list__item-name">Step 1 - Get View Token</div></div><div class="sc-fzoWqW gqsYtg"><div class="sc-fzplgP jPtEHd">POST</div><div class="sc-fzonjX jMaMuX documentation-core-list__item-name">Step 2 - Get Final Session Token</div></div></div><div class="sc-fzoMdx fYHVvJ documentation-core-list__item-icon"><div class="sc-fznzqM dYlvIp core-icon-button"><i color="content-color-primary" class="IconWrapper__IconContainer-gnjn48-0 kmurjI" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M9.29282 7.99996L5.64637 11.6464L6.35348 12.3535L10.707 7.99996L6.35348 3.64641L5.64637 4.35352L9.29282 7.99996Z" fill="#6B6B6B"></path></svg></i></div><div color="9c274c" class="sc-fznOgF hWmdUF"><i color="content-color-secondary" class="IconWrapper__IconContainer-gnjn48-0 LzfYz" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.5 1C1.67157 1 1 1.67157 1 2.5V13.5C1 14.3284 1.67157 15 2.5 15H13.5C14.3284 15 15 14.3284 15 13.5V4.5C15 3.67157 14.3284 3 13.5 3H8.70711L7.14645 1.43934C6.86514 1.15803 6.48361 1 6.08579 1H2.5ZM2 2.5C2 2.22386 2.22386 2 2.5 2H6.08579C6.21839 2 6.34557 2.05268 6.43934 2.14645L8.14645 3.85355C8.24021 3.94732 8.36739 4 8.5 4H13.5C13.7761 4 14 4.22386 14 4.5V6H2V2.5ZM2 7V13.5C2 13.7761 2.22386 14 2.5 14H13.5C13.7761 14 14 13.7761 14 13.5V7H2Z" fill="#6B6B6B"></path></svg></i></div><div class="sc-fznYue kfdnyY documentation-core-list__item-name" style="text-decoration: initial;">Login</div></div><div class="sc-fzoMdx fYHVvJ documentation-core-list__item-icon"><div class="sc-fznzqM dYlvIp core-icon-button"><i color="content-color-primary" class="IconWrapper__IconContainer-gnjn48-0 kmurjI" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M9.29282 7.99996L5.64637 11.6464L6.35348 12.3535L10.707 7.99996L6.35348 3.64641L5.64637 4.35352L9.29282 7.99996Z" fill="#6B6B6B"></path></svg></i></div><div color="9c274c" class="sc-fznOgF hWmdUF"><i color="content-color-secondary" class="IconWrapper__IconContainer-gnjn48-0 LzfYz" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.5 1C1.67157 1 1 1.67157 1 2.5V13.5C1 14.3284 1.67157 15 2.5 15H13.5C14.3284 15 15 14.3284 15 13.5V4.5C15 3.67157 14.3284 3 13.5 3H8.70711L7.14645 1.43934C6.86514 1.15803 6.48361 1 6.08579 1H2.5ZM2 2.5C2 2.22386 2.22386 2 2.5 2H6.08579C6.21839 2 6.34557 2.05268 6.43934 2.14645L8.14645 3.85355C8.24021 3.94732 8.36739 4 8.5 4H13.5C13.7761 4 14 4.22386 14 4.5V6H2V2.5ZM2 7V13.5C2 13.7761 2.22386 14 2.5 14H13.5C13.7761 14 14 13.7761 14 13.5V7H2Z" fill="#6B6B6B"></path></svg></i></div><div class="sc-fznYue kfdnyY documentation-core-list__item-name" style="text-decoration: initial;">Scrip Master</div></div><div class="sc-fzoMdx fYHVvJ documentation-core-list__item-icon"><div class="sc-fznzqM dYlvIp core-icon-button"><i color="content-color-primary" class="IconWrapper__IconContainer-gnjn48-0 kmurjI" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M9.29282 7.99996L5.64637 11.6464L6.35348 12.3535L10.707 7.99996L6.35348 3.64641L5.64637 4.35352L9.29282 7.99996Z" fill="#6B6B6B"></path></svg></i></div><div color="9c274c" class="sc-fznOgF hWmdUF"><i color="content-color-secondary" class="IconWrapper__IconContainer-gnjn48-0 LzfYz" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.5 1C1.67157 1 1 1.67157 1 2.5V13.5C1 14.3284 1.67157 15 2.5 15H13.5C14.3284 15 15 14.3284 15 13.5V4.5C15 3.67157 14.3284 3 13.5 3H8.70711L7.14645 1.43934C6.86514 1.15803 6.48361 1 6.08579 1H2.5ZM2 2.5C2 2.22386 2.22386 2 2.5 2H6.08579C6.21839 2 6.34557 2.05268 6.43934 2.14645L8.14645 3.85355C8.24021 3.94732 8.36739 4 8.5 4H13.5C13.7761 4 14 4.22386 14 4.5V6H2V2.5ZM2 7V13.5C2 13.7761 2.22386 14 2.5 14H13.5C13.7761 14 14 13.7761 14 13.5V7H2Z" fill="#6B6B6B"></path></svg></i></div><div class="sc-fznYue kfdnyY documentation-core-list__item-name" style="text-decoration: initial;">Orders</div></div><div class="sc-fzoMdx fYHVvJ documentation-core-list__item-icon"><div class="sc-fznzqM dYlvIp core-icon-button"><i color="content-color-primary" class="IconWrapper__IconContainer-gnjn48-0 kmurjI" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M9.29282 7.99996L5.64637 11.6464L6.35348 12.3535L10.707 7.99996L6.35348 3.64641L5.64637 4.35352L9.29282 7.99996Z" fill="#6B6B6B"></path></svg></i></div><div color="9c274c" class="sc-fznOgF hWmdUF"><i color="content-color-secondary" class="IconWrapper__IconContainer-gnjn48-0 LzfYz" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.5 1C1.67157 1 1 1.67157 1 2.5V13.5C1 14.3284 1.67157 15 2.5 15H13.5C14.3284 15 15 14.3284 15 13.5V4.5C15 3.67157 14.3284 3 13.5 3H8.70711L7.14645 1.43934C6.86514 1.15803 6.48361 1 6.08579 1H2.5ZM2 2.5C2 2.22386 2.22386 2 2.5 2H6.08579C6.21839 2 6.34557 2.05268 6.43934 2.14645L8.14645 3.85355C8.24021 3.94732 8.36739 4 8.5 4H13.5C13.7761 4 14 4.22386 14 4.5V6H2V2.5ZM2 7V13.5C2 13.7761 2.22386 14 2.5 14H13.5C13.7761 14 14 13.7761 14 13.5V7H2Z" fill="#6B6B6B"></path></svg></i></div><div class="sc-fznYue kfdnyY documentation-core-list__item-name" style="text-decoration: initial;">Order Report</div></div><div class="sc-fzoMdx fYHVvJ documentation-core-list__item-icon"><div class="sc-fznzqM dYlvIp core-icon-button"><i color="content-color-primary" class="IconWrapper__IconContainer-gnjn48-0 kmurjI" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M9.29282 7.99996L5.64637 11.6464L6.35348 12.3535L10.707 7.99996L6.35348 3.64641L5.64637 4.35352L9.29282 7.99996Z" fill="#6B6B6B"></path></svg></i></div><div color="9c274c" class="sc-fznOgF hWmdUF"><i color="content-color-secondary" class="IconWrapper__IconContainer-gnjn48-0 LzfYz" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.5 1C1.67157 1 1 1.67157 1 2.5V13.5C1 14.3284 1.67157 15 2.5 15H13.5C14.3284 15 15 14.3284 15 13.5V4.5C15 3.67157 14.3284 3 13.5 3H8.70711L7.14645 1.43934C6.86514 1.15803 6.48361 1 6.08579 1H2.5ZM2 2.5C2 2.22386 2.22386 2 2.5 2H6.08579C6.21839 2 6.34557 2.05268 6.43934 2.14645L8.14645 3.85355C8.24021 3.94732 8.36739 4 8.5 4H13.5C13.7761 4 14 4.22386 14 4.5V6H2V2.5ZM2 7V13.5C2 13.7761 2.22386 14 2.5 14H13.5C13.7761 14 14 13.7761 14 13.5V7H2Z" fill="#6B6B6B"></path></svg></i></div><div class="sc-fznYue kfdnyY documentation-core-list__item-name" style="text-decoration: initial;">Positions</div></div><div class="sc-fzoMdx fYHVvJ documentation-core-list__item-icon"><div class="sc-fznzqM dYlvIp core-icon-button"><i color="content-color-primary" class="IconWrapper__IconContainer-gnjn48-0 kmurjI" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M9.29282 7.99996L5.64637 11.6464L6.35348 12.3535L10.707 7.99996L6.35348 3.64641L5.64637 4.35352L9.29282 7.99996Z" fill="#6B6B6B"></path></svg></i></div><div color="9c274c" class="sc-fznOgF hWmdUF"><i color="content-color-secondary" class="IconWrapper__IconContainer-gnjn48-0 LzfYz" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.5 1C1.67157 1 1 1.67157 1 2.5V13.5C1 14.3284 1.67157 15 2.5 15H13.5C14.3284 15 15 14.3284 15 13.5V4.5C15 3.67157 14.3284 3 13.5 3H8.70711L7.14645 1.43934C6.86514 1.15803 6.48361 1 6.08579 1H2.5ZM2 2.5C2 2.22386 2.22386 2 2.5 2H6.08579C6.21839 2 6.34557 2.05268 6.43934 2.14645L8.14645 3.85355C8.24021 3.94732 8.36739 4 8.5 4H13.5C13.7761 4 14 4.22386 14 4.5V6H2V2.5ZM2 7V13.5C2 13.7761 2.22386 14 2.5 14H13.5C13.7761 14 14 13.7761 14 13.5V7H2Z" fill="#6B6B6B"></path></svg></i></div><div class="sc-fznYue kfdnyY documentation-core-list__item-name" style="text-decoration: initial;">Portfolio</div></div><div class="sc-fzoMdx fYHVvJ documentation-core-list__item-icon"><div class="sc-fznzqM dYlvIp core-icon-button"><i color="content-color-primary" class="IconWrapper__IconContainer-gnjn48-0 kmurjI" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M9.29282 7.99996L5.64637 11.6464L6.35348 12.3535L10.707 7.99996L6.35348 3.64641L5.64637 4.35352L9.29282 7.99996Z" fill="#6B6B6B"></path></svg></i></div><div color="9c274c" class="sc-fznOgF hWmdUF"><i color="content-color-secondary" class="IconWrapper__IconContainer-gnjn48-0 LzfYz" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.5 1C1.67157 1 1 1.67157 1 2.5V13.5C1 14.3284 1.67157 15 2.5 15H13.5C14.3284 15 15 14.3284 15 13.5V4.5C15 3.67157 14.3284 3 13.5 3H8.70711L7.14645 1.43934C6.86514 1.15803 6.48361 1 6.08579 1H2.5ZM2 2.5C2 2.22386 2.22386 2 2.5 2H6.08579C6.21839 2 6.34557 2.05268 6.43934 2.14645L8.14645 3.85355C8.24021 3.94732 8.36739 4 8.5 4H13.5C13.7761 4 14 4.22386 14 4.5V6H2V2.5ZM2 7V13.5C2 13.7761 2.22386 14 2.5 14H13.5C13.7761 14 14 13.7761 14 13.5V7H2Z" fill="#6B6B6B"></path></svg></i></div><div class="sc-fznYue kfdnyY documentation-core-list__item-name" style="text-decoration: initial;">Limits</div></div><div class="sc-fzoMdx fYHVvJ documentation-core-list__item-icon"><div class="sc-fznzqM dYlvIp core-icon-button"><i color="content-color-primary" class="IconWrapper__IconContainer-gnjn48-0 kmurjI" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M9.29282 7.99996L5.64637 11.6464L6.35348 12.3535L10.707 7.99996L6.35348 3.64641L5.64637 4.35352L9.29282 7.99996Z" fill="#6B6B6B"></path></svg></i></div><div color="9c274c" class="sc-fznOgF hWmdUF"><i color="content-color-secondary" class="IconWrapper__IconContainer-gnjn48-0 LzfYz" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.5 1C1.67157 1 1 1.67157 1 2.5V13.5C1 14.3284 1.67157 15 2.5 15H13.5C14.3284 15 15 14.3284 15 13.5V4.5C15 3.67157 14.3284 3 13.5 3H8.70711L7.14645 1.43934C6.86514 1.15803 6.48361 1 6.08579 1H2.5ZM2 2.5C2 2.22386 2.22386 2 2.5 2H6.08579C6.21839 2 6.34557 2.05268 6.43934 2.14645L8.14645 3.85355C8.24021 3.94732 8.36739 4 8.5 4H13.5C13.7761 4 14 4.22386 14 4.5V6H2V2.5ZM2 7V13.5C2 13.7761 2.22386 14 2.5 14H13.5C13.7761 14 14 13.7761 14 13.5V7H2Z" fill="#6B6B6B"></path></svg></i></div><div class="sc-fznYue kfdnyY documentation-core-list__item-name" style="text-decoration: initial;">Margin</div></div><div class="sc-fzoMdx fYHVvJ documentation-core-list__item-icon"><div class="sc-fznzqM dYlvIp core-icon-button"><i color="content-color-primary" class="IconWrapper__IconContainer-gnjn48-0 kmurjI" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M9.29282 7.99996L5.64637 11.6464L6.35348 12.3535L10.707 7.99996L6.35348 3.64641L5.64637 4.35352L9.29282 7.99996Z" fill="#6B6B6B"></path></svg></i></div><div color="9c274c" class="sc-fznOgF hWmdUF"><i color="content-color-secondary" class="IconWrapper__IconContainer-gnjn48-0 LzfYz" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.5 1C1.67157 1 1 1.67157 1 2.5V13.5C1 14.3284 1.67157 15 2.5 15H13.5C14.3284 15 15 14.3284 15 13.5V4.5C15 3.67157 14.3284 3 13.5 3H8.70711L7.14645 1.43934C6.86514 1.15803 6.48361 1 6.08579 1H2.5ZM2 2.5C2 2.22386 2.22386 2 2.5 2H6.08579C6.21839 2 6.34557 2.05268 6.43934 2.14645L8.14645 3.85355C8.24021 3.94732 8.36739 4 8.5 4H13.5C13.7761 4 14 4.22386 14 4.5V6H2V2.5ZM2 7V13.5C2 13.7761 2.22386 14 2.5 14H13.5C13.7761 14 14 13.7761 14 13.5V7H2Z" fill="#6B6B6B"></path></svg></i></div><div class="sc-fznYue kfdnyY documentation-core-list__item-name" style="text-decoration: initial;">Quotes</div></div><div class="sc-fzoMdx fYHVvJ documentation-core-list__item-icon"><div class="sc-fznzqM dYlvIp core-icon-button"><i color="content-color-primary" class="IconWrapper__IconContainer-gnjn48-0 kmurjI" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M9.29282 7.99996L5.64637 11.6464L6.35348 12.3535L10.707 7.99996L6.35348 3.64641L5.64637 4.35352L9.29282 7.99996Z" fill="#6B6B6B"></path></svg></i></div><div color="9c274c" class="sc-fznOgF hWmdUF"><i color="content-color-secondary" class="IconWrapper__IconContainer-gnjn48-0 LzfYz" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.5 1C1.67157 1 1 1.67157 1 2.5V13.5C1 14.3284 1.67157 15 2.5 15H13.5C14.3284 15 15 14.3284 15 13.5V4.5C15 3.67157 14.3284 3 13.5 3H8.70711L7.14645 1.43934C6.86514 1.15803 6.48361 1 6.08579 1H2.5ZM2 2.5C2 2.22386 2.22386 2 2.5 2H6.08579C6.21839 2 6.34557 2.05268 6.43934 2.14645L8.14645 3.85355C8.24021 3.94732 8.36739 4 8.5 4H13.5C13.7761 4 14 4.22386 14 4.5V6H2V2.5ZM2 7V13.5C2 13.7761 2.22386 14 2.5 14H13.5C13.7761 14 14 13.7761 14 13.5V7H2Z" fill="#6B6B6B"></path></svg></i></div><div class="sc-fznYue kfdnyY documentation-core-list__item-name" style="text-decoration: initial;">Websocket</div></div></div></div><div class="layoutstyles__ContentWrapper-sc-sfl92e-5 fBVyPO"><main id="intro" class="doc-bodystyles__DocBodyContainer-sc-6a86lm-0 fiPtFd"><section id="doc-wrapper" class="sc-fznLxA doc-bodystyles__DocWrapper-sc-6a86lm-2 gQRraV hyZgNl"><section class="entity-layoutstyles__EntityLayoutContainer-sc-123p0b7-1 PkPef"><div class="collectionstyles__CollectionWrapper-sc-nm5878-0 bfQVFC"><div class="sc-fzolEj jJAzoG"><div class="sc-fzqBkg fuZJw"><h1 class="sc-fzqPZZ hQgKkw"><span class="sc-fzoxKX eJUqAq">Kotak Neo Trade API 1.0.0</span></h1></div><div class="sc-fzpkJw gEiHfF"><div class="sc-fzpans hcfROc"><h2 class="sc-fznyAO fTNtXF"><span>Welcome to Kotak NEO APIs - the future of trading</span></h2><span>
</span><p><span>Build your own Trading Platform Today</span></p><span>
</span><p><span>Kotak Neo’s powerful REST APIs can help you develop your own trading Terminal and implement your strategies easily and intuitively. Our APIs give you open access to live market feeds, orders, live positions, and much more.</span></p><span>
</span><h2 class="sc-fznyAO fTNtXF"><span>What are the benefits of our APIs?</span></h2><span>
</span><h4 class="sc-fznZeY dxgVpD"><span>Absolutely Free, No strings attached.</span></h4><span>
</span><p><span>We charge you absolutely nothing! Build your own trading terminal at no cost!</span></p><span>
</span><h4 class="sc-fznZeY dxgVpD"><span>User friendly</span></h4><span>
</span><p><span>Our state-of-the-art APIs help you create user-friendly end-to-end broking services without breaking a sweat.</span></p><span>
</span><h4 class="sc-fznZeY dxgVpD"><span>Our Service</span></h4><span>
</span><p><span>With years of experience, we’re proud to offer a truly market-leading service. You will also be associated with a well-established brand and a full-service broker.</span></p><span>
</span><h4 class="sc-fznZeY dxgVpD"><span>Dependable support</span></h4><span>
</span><p><span>You can count on our dedicated support team for a quick turnaround for all your queries.</span></p><span>
</span><h4 class="sc-fznZeY dxgVpD"><span>Real-time execution</span></h4><span>
</span><p><span>Execute real-time transactions supported by live data feeds.</span></p><span>
</span><h4 class="sc-fznZeY dxgVpD"><span>Fast and seamless</span></h4><span>
</span><p><span>You can place up to 20 orders per second and 200 orders per min.</span></p><span>
</span></div></div></div></div><div class="entity-layoutstyles__SecondaryLayout-sc-123p0b7-0 kNaRJP"></div></section><section id="b3d2e9e9-61e2-4646-9180-d463d1d0b428" style="margin-bottom: 50px;"><div style="height: 1047px;"></div></section><section id="c92c252d-b435-4133-8a65-16324a02ee3c" style="margin-bottom: 50px;"><div style="height: 624px;"></div></section><section id="efedba89-1ab9-4c57-93bb-3e88c16ee2e8" style="margin-bottom: 50px;"><div style="height: 777px;"></div></section><section id="545da69c-fa67-4f6c-80b2-d781e4a4db57" style="margin-bottom: 50px;"><div style="height: 1579px;"></div></section><section id="be670d3b-c280-46c6-85b1-4205de002860" style="margin-bottom: 50px;"><div style="height: 1995px;"></div></section><section id="39d380c0-2bcf-476f-a5cb-9e58a897cf75" style="margin-bottom: 50px;"><div style="height: 289px;"></div></section><section id="d7dadbe5-954f-4b80-b6ea-45981593f92c" style="margin-bottom: 50px;"><div style="height: 1794px;"></div></section><section id="49c37e75-9518-4d89-8a85-217234aca1fd" style="margin-bottom: 50px;"><div style="height: 1516px;"></div></section><section id="be00336c-1bfd-47d5-99c7-4337213b2d41" style="margin-bottom: 50px;"><div style="height: 1936px;"></div></section><section id="1a2a5fad-4a0f-4ac9-86b5-96a3a3d6902a" style="margin-bottom: 50px;"><div style="height: 89px;"></div></section><section id="226e8b5b-d86c-4b9c-a281-8e6cabc91e38" style="margin-bottom: 50px;"><div style="height: 1516px;"></div></section><section id="0587aa33-bf70-49da-8b3d-b2352ad97695" style="margin-bottom: 50px;"><div style="height: 189px;"></div></section><section id="015d05a9-0cfe-418e-aa8c-6e07af716636" style="margin-bottom: 50px;"><div style="height: 2492px;"></div></section><section id="cd41c2be-4054-4202-a128-90f92b1f3ad6" style="margin-bottom: 50px;"><div style="height: 2931px;"></div></section><section id="7f129119-fc7f-4beb-a789-015b9fa50f70" style="margin-bottom: 50px;"><div style="height: 2652px;"></div></section><section id="104839d1-0f6b-4ee2-909c-5c26b8864bb7" style="margin-bottom: 50px;"><div style="height: 2097px;"></div></section><section id="d3567fd9-3e08-4e09-a132-a7497e819c8e" style="margin-bottom: 50px;"><div style="height: 2097px;"></div></section><section id="bbf770d9-cc2b-4536-b1f5-0f9bd75e6788" style="margin-bottom: 50px;"><div style="height: 89px;"></div></section><section id="ff196455-54ff-4b46-88f8-4e4aad0ef7d4" style="margin-bottom: 50px;"><div style="height: 2461px;"></div></section><section id="3a4989bb-c50b-464d-9c4b-925f33f89126" style="margin-bottom: 50px;"><div style="height: 2692px;"></div></section><section id="b0e974fd-7030-45ec-891d-9b0410ad3ff1" style="margin-bottom: 50px;"><div style="height: 2377px;"></div></section><section id="344a9b99-3c88-42ab-9f08-1bc91545fac0" style="margin-bottom: 50px;"><div style="height: 389px;"></div></section><section id="143cd67b-8ca3-48a4-bdc8-3be1f211fcf5" style="margin-bottom: 50px;"><div style="height: 2101px;"></div></section><section id="afa732d7-6919-4d30-8e7b-df04a4cebe91" style="margin-bottom: 50px;"><div style="height: 89px;"></div></section><section id="442fc7b6-5aa6-4868-808a-2783addd5919" style="margin-bottom: 50px;"><div style="height: 1258px;"></div></section><section id="119be5ac-39c8-49f5-9a7a-db9c4056705d" style="margin-bottom: 50px;"><div style="height: 89px;"></div></section><section id="7802cae7-ba62-4a2e-bc9f-66c31be35286" style="margin-bottom: 50px;"><div style="height: 2663px;"></div></section><section id="6ed4dfe5-c5b0-4ed2-bfb1-57a49b034144" style="margin-bottom: 50px;"><div style="height: 89px;"></div></section><section id="5de5cddf-157a-48d0-9fbb-cddf05d78fce" style="margin-bottom: 50px;"><div style="height: 3235px;"></div></section><section id="ecc5c185-2f0c-4ca6-81f7-2b60fe87ca43" style="margin-bottom: 0px;"><section id="ecc5c185-2f0c-4ca6-81f7-2b60fe87ca43" class="entitystyles__FolderContainer-sc-kfteh-6 dmqQXo"><div class="entitystyles__FolderDataWrapper-sc-kfteh-5 ebdMLC"><div class="sc-fzomME hTHjyG documentation-core-item-name__text"><h2 class="sc-fzqAbL hLoBGr"><div class="sc-fzqMAW jYXJBZ">Quotes</div></h2></div><div class="sc-fzpkJw gEiHfF"><div class="sc-fzpans hcfROc"><p><span>URL : </span><a class="sc-fznMAR fNVHaE markdown-link" href="https://gw-napi.kotaksecurities.com/apim/quotes/1.0/quotes/neosymbol/%7BneoSymbols%7D/%7BquoteType%7D" target="_blank" rel="noreferrer noopener nofollow"><span>https://gw-napi.kotaksecurities.com/apim/quotes/1.0/quotes/neosymbol/{neoSymbols}/{quoteType}</span></a></p><span>
</span><p><span>Example parameters to be passed in neoSymbols : A comma-separated list of neosymbols to fetch quotes</span></p><span>
</span><p><span>neoSymbols (string) - nse_cm|2885,bse_cm|532174,nse_fo|65500,bse_cm|540376</span></p><span>
</span><p><span>Example of quoteType -</span></p><span>
</span><p><span>quoteType (string) - Type of data to retrieve. Options include&nbsp;</span><code><span>ltp</span></code><span>,&nbsp;</span><code><span>depth</span></code><span>,&nbsp;</span><code><span>oi</span></code><span>,&nbsp;</span><code><span>ohlc</span></code><span>,&nbsp;</span><code><span>circuit_limits</span></code><span>,&nbsp;</span><code><span>scrip_details</span></code><span>,&nbsp;</span><code><span>52W</span></code><span>, or&nbsp;</span><code><span>all</span></code><span>. Defaults to&nbsp;</span><code><span>all</span></code><span>.</span></p><span>
</span></div></div></div><div class="entitystyles__CodeblockPlaceholder-sc-kfteh-4 yJneL"></div></section></section><section id="a6abb97b-f1f4-49d5-9e76-4464abfba2ee" style="margin-bottom: 0px;"><section id="a6abb97b-f1f4-49d5-9e76-4464abfba2ee" class="entitystyles__RequestContainer-sc-kfteh-7 hhPQzw"><div class="entitystyles__EntityRequest-sc-kfteh-3 cRwEHS"><div class="sc-fzpkqZ eEJfqH documentation-core-item-name__text"><h3 class="sc-fznLPX cxTVKx"><span class="sc-fzoaKM diFeSb">GET</span><span class="sc-fzomuh eaYntv documentation-core-item-request-name">Quotes</span></h3></div><div class="sc-fzqMdD iILLqY"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 gaFYqu url-text" data-aether-id="aether-text" data-testid="aether-text" data-click="">https://gw-napi.kotaksecurities.com/apim/quotes/1.0/quotes/neosymbol/nse_cm%7C2885%2Cbse_cm%7C532174%2Cnse_fo%7C65500%2Cbse_cm%7C540376/all</span><button type="tertiary" class="Button__StyledButton-b8af3e-1 klCLOJ url-copy-btn" data-aether-id="aether-button" data-testid="aether-button" data-click=""><i class="IconWrapper__IconContainer-gnjn48-0 dkgnYV aether-button__left-icon" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2 9.5C2 9.77614 2.22386 10 2.5 10H3V11H2.5C1.67157 11 1 10.3284 1 9.5V2.5C1 1.67157 1.67157 1 2.5 1H9.5C10.3284 1 11 1.67157 11 2.5V3H10V2.5C10 2.22386 9.77614 2 9.5 2H2.5C2.22386 2 2 2.22386 2 2.5V9.5Z" fill="#6B6B6B"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M6.5 5C5.67157 5 5 5.67157 5 6.5V13.5C5 14.3284 5.67157 15 6.5 15H13.5C14.3284 15 15 14.3284 15 13.5V6.5C15 5.67157 14.3284 5 13.5 5H6.5ZM6 6.5C6 6.22386 6.22386 6 6.5 6H13.5C13.7761 6 14 6.22386 14 6.5V13.5C14 13.7761 13.7761 14 13.5 14H6.5C6.22386 14 6 13.7761 6 13.5V6.5Z" fill="#6B6B6B"></path></svg></i></button></div><div class="sc-fzpkJw gEiHfF"><div class="sc-fzpans hcfROc"><p><span>Generated from cURL: curl -X 'GET' </span><br><span>  '</span><a class="sc-fznMAR fNVHaE markdown-link" href="https://gw-napi.kotaksecurities.com/apim/quotes/1.0/quotes/neosymbol/nse_cm%7C2885%2Cbse_cm%7C532174%2Cnse_fo%7C65500%2Cbse_cm%7C540376/all" target="_blank" rel="noreferrer noopener nofollow"><span>https://gw-napi.kotaksecurities.com/apim/quotes/1.0/quotes/neosymbol/nse_cm%7C2885%2Cbse_cm%7C532174%2Cnse_fo%7C65500%2Cbse_cm%7C540376/all</span></a><span>' </span><br><span>  -H 'accept: application/json' </span><br><span>  -H 'Authorization: Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'</span></p><span>
</span></div></div><div class="sc-fzqzlV jLJgXG"><span>HEADERS</span></div><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 gCATsr kvd-table " data-testid="aether-flex" data-click=""><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 koJEiu sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">accept</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 eWenhA" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">application/json</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 koJEiu sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">Authorization</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 eWenhA" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************</p></div></div></div></div></div><div class="sc-fzowVh entitystyles__EntityExampleContainer-sc-kfteh-1 juoKlG bKQSne"><div class="entitystyles__ExampleWrapper-sc-kfteh-8 gCzHwU"><div class="sc-fzoJus hgiEjX"><div class="sc-fzoVTD cgEwGC">Example Request</div><div class="sc-fzpisO jHQZJv"><div data-aether-id="aether-dropdown" class="DropdownContainer__StyledDropdownContainer-bmd2h5-0 dFENEc"><div class="SingleSelect__StyledReactSelect-sc-1dz28nt-0 iIRTfv example-request-dropdown css-nxiuxh-container"><span id="react-select-30-live-region" class="css-7pg0cj-a11yText"></span><span aria-live="polite" aria-atomic="false" aria-relevant="additions text" class="css-7pg0cj-a11yText"></span><div class="aether-dropdown__control css-14z9jjs-control"><div class="aether-dropdown__value-container aether-dropdown__value-container--has-value css-1s8hhp9"><div class="aether-dropdown__single-value css-15leyl8-singleValue">Get Quotes</div><input id="react-select-30-input" tabindex="0" inputmode="none" aria-autocomplete="list" aria-expanded="false" aria-haspopup="true" aria-controls="react-select-30-listbox" aria-owns="react-select-30-listbox" role="combobox" aria-readonly="true" class="css-1hac4vs-dummyInput" value=""></div><div class="aether-dropdown__indicators css-1wy0on6"><div class="aether-dropdown__indicator aether-dropdown__dropdown-indicator css-tlfecz-indicatorContainer" aria-hidden="true"><i class="IconWrapper__IconContainer-gnjn48-0 hImIRl" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.00004 9.29294L4.35359 5.64649L3.64648 6.3536L8.00004 10.7072L12.3536 6.3536L11.6465 5.64649L8.00004 9.29294Z" fill="#6B6B6B"></path></svg></i></div></div></div></div></div></div></div><div class="sc-AxirZ dRRFNE click-to-expand-overlay-container"><div class="sc-AxjAm ieiRVc click-to-expand-overlay"><button data-entity-id="click-to-expand-button" class="sc-AxiKw kcukAz highlighted-code__expand-button">View More</button></div><div class="sc-AxheI fQa-DMo highlighted-code--documentation"><div class="sc-Axmtr rytkq"><div class="sc-AxmLO dTwZbd highlighted-code__language-label">dart</div><div class="sc-fzozJi gQbNdh"><button type="tertiary" class="Button__StyledButton-b8af3e-1 gaHlYy highlighted-code__config-button" data-aether-id="aether-button" data-testid="aether-button" data-click=""><i class="IconWrapper__IconContainer-gnjn48-0 dkgnYV aether-button__left-icon" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M15 3H1V2H15V3Z" fill="#6B6B6B"></path><path d="M12 8H1V7H12C13.6569 7 15 8.34315 15 10C15 11.6569 13.6569 13 12 13H9.70712L11.3536 14.6464L10.6465 15.3536L7.79291 12.5L10.6465 9.64645L11.3536 10.3536L9.70712 12H12C13.1046 12 14 11.1046 14 10C14 8.89543 13.1046 8 12 8Z" fill="#6B6B6B"></path><path d="M1 13H6V12H1V13Z" fill="#6B6B6B"></path></svg></i></button><button type="tertiary" class="Button__StyledButton-b8af3e-1 gaHlYy highlighted-code__config-button" data-aether-id="aether-button" data-testid="aether-button" data-click=""><i class="IconWrapper__IconContainer-gnjn48-0 dkgnYV aether-button__left-icon" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2 9.5C2 9.77614 2.22386 10 2.5 10H3V11H2.5C1.67157 11 1 10.3284 1 9.5V2.5C1 1.67157 1.67157 1 2.5 1H9.5C10.3284 1 11 1.67157 11 2.5V3H10V2.5C10 2.22386 9.77614 2 9.5 2H2.5C2.22386 2 2 2.22386 2 2.5V9.5Z" fill="#6B6B6B"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M6.5 5C5.67157 5 5 5.67157 5 6.5V13.5C5 14.3284 5.67157 15 6.5 15H13.5C14.3284 15 15 14.3284 15 13.5V6.5C15 5.67157 14.3284 5 13.5 5H6.5ZM6 6.5C6 6.22386 6.22386 6 6.5 6H13.5C13.7761 6 14 6.22386 14 6.5V13.5C14 13.7761 13.7761 14 13.5 14H6.5C6.22386 14 6 13.7761 6 13.5V6.5Z" fill="#6B6B6B"></path></svg></i></button></div></div><div class="sc-fzoLsD fYZyZu"><div data-entity-type="code-block" class="sc-AxhCb kEsiDh"><div class="sc-AxgMl khKvgz highlight-code"><pre class="language-dart"><code class="language-dart highlighted-code__code"><span class="token keyword">var</span> headers <span class="token operator">=</span> <span class="token punctuation">{</span>
  <span class="token string-property property">'accept'</span><span class="token operator">:</span> <span class="token string">'application/json'</span><span class="token punctuation">,</span>
  <span class="token string-property property">'Authorization'</span><span class="token operator">:</span> <span class="token string">'Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token keyword">var</span> request <span class="token operator">=</span> http<span class="token punctuation">.</span><span class="token function">Request</span><span class="token punctuation">(</span><span class="token string">'GET'</span><span class="token punctuation">,</span> Uri<span class="token punctuation">.</span><span class="token function">parse</span><span class="token punctuation">(</span><span class="token string">'https://gw-napi.kotaksecurities.com/apim/quotes/1.0/quotes/neosymbol/nse_cm%7C2885%2Cbse_cm%7C532174%2Cnse_fo%7C65500%2Cbse_cm%7C540376/all'</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

request<span class="token punctuation">.</span>headers<span class="token punctuation">.</span><span class="token function">addAll</span><span class="token punctuation">(</span>headers<span class="token punctuation">)</span><span class="token punctuation">;</span>

http<span class="token punctuation">.</span>StreamedResponse response <span class="token operator">=</span> <span class="token keyword">await</span> request<span class="token punctuation">.</span><span class="token function">send</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token keyword">if</span> <span class="token punctuation">(</span>response<span class="token punctuation">.</span>statusCode <span class="token operator">==</span> <span class="token number">200</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  <span class="token function">print</span><span class="token punctuation">(</span><span class="token keyword">await</span> response<span class="token punctuation">.</span>stream<span class="token punctuation">.</span><span class="token function">bytesToString</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
<span class="token keyword">else</span> <span class="token punctuation">{</span>
  <span class="token function">print</span><span class="token punctuation">(</span>response<span class="token punctuation">.</span>reasonPhrase<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre></div></div></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 gDQNWk styles__ExampleResponseContainer-sc-1gk0rxh-3 jXmOln " data-testid="aether-flex" data-click=""><div color="grey" data-aether-id="aether-tag" data-testid="aether-tag" class="Tag__StyledTag-h8sm2k-0 fqa-dcl styles__ExampleResponseTag-sc-1gk0rxh-4 TttxM"><span color="grey" class="Tag__StyledTagText-h8sm2k-1 AQtIR">200 OK</span></div><div class="sc-fznyYp jxzYTh">Example Response</div><div class="Tabs__StyledTabsWrapper-e9ehwv-0 kBTgJB" data-aether-id="aether-tabs" data-testid="aether-tabs" data-click=""><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 gDQNWk  " data-testid="aether-flex" data-click=""><ul class="TabList__StyledTabList-xqq1cg-0 ipIhZz" type="primary"><li type="primary" class="Tab__StyledPrimaryTabWrapper-mv7d6n-3 csIoCK"><button class="Tab__StyledTab-mv7d6n-0 cAEscj"><span type="" color="" class="Text__TextContainer-sc-1kqigik-0 dLPxIq" data-aether-id="aether-text" data-testid="aether-text" data-click="">Body</span></button></li><li type="primary" class="Tab__StyledPrimaryTabWrapper-mv7d6n-3 eodlZY"><button class="Tab__StyledTab-mv7d6n-0 bsbHut"><span type="" color="" class="Text__TextContainer-sc-1kqigik-0 dLPxIq" data-aether-id="aether-text" data-testid="aether-text" data-click="">Headers (30)</span></button></li></ul></div><div class="TabPanels__StyledTabPanel-sc-1xflqlv-0 iLClOP"><div class="TabPanel__TabPanelElement-sc-1u751ph-0 iUQkGb"><div class="sc-AxirZ dRRFNE click-to-expand-overlay-container"><div class="sc-AxjAm ieiRVc click-to-expand-overlay"><button data-entity-id="click-to-expand-button" class="sc-AxiKw kcukAz highlighted-code__expand-button">View More</button></div><div class="sc-AxheI fQa-DMo highlighted-code--documentation"><div class="sc-Axmtr rytkq"><div class="sc-AxmLO dTwZbd highlighted-code__language-label">json</div><div class="sc-fzozJi gQbNdh"><button type="tertiary" class="Button__StyledButton-b8af3e-1 gaHlYy highlighted-code__config-button" data-aether-id="aether-button" data-testid="aether-button" data-click=""><i class="IconWrapper__IconContainer-gnjn48-0 dkgnYV aether-button__left-icon" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M15 3H1V2H15V3Z" fill="#6B6B6B"></path><path d="M12 8H1V7H12C13.6569 7 15 8.34315 15 10C15 11.6569 13.6569 13 12 13H9.70712L11.3536 14.6464L10.6465 15.3536L7.79291 12.5L10.6465 9.64645L11.3536 10.3536L9.70712 12H12C13.1046 12 14 11.1046 14 10C14 8.89543 13.1046 8 12 8Z" fill="#6B6B6B"></path><path d="M1 13H6V12H1V13Z" fill="#6B6B6B"></path></svg></i></button><button type="tertiary" class="Button__StyledButton-b8af3e-1 gaHlYy highlighted-code__config-button" data-aether-id="aether-button" data-testid="aether-button" data-click=""><i class="IconWrapper__IconContainer-gnjn48-0 dkgnYV aether-button__left-icon" title=""><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2 9.5C2 9.77614 2.22386 10 2.5 10H3V11H2.5C1.67157 11 1 10.3284 1 9.5V2.5C1 1.67157 1.67157 1 2.5 1H9.5C10.3284 1 11 1.67157 11 2.5V3H10V2.5C10 2.22386 9.77614 2 9.5 2H2.5C2.22386 2 2 2.22386 2 2.5V9.5Z" fill="#6B6B6B"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M6.5 5C5.67157 5 5 5.67157 5 6.5V13.5C5 14.3284 5.67157 15 6.5 15H13.5C14.3284 15 15 14.3284 15 13.5V6.5C15 5.67157 14.3284 5 13.5 5H6.5ZM6 6.5C6 6.22386 6.22386 6 6.5 6H13.5C13.7761 6 14 6.22386 14 6.5V13.5C14 13.7761 13.7761 14 13.5 14H6.5C6.22386 14 6 13.7761 6 13.5V6.5Z" fill="#6B6B6B"></path></svg></i></button></div></div><div class="sc-fzoLsD fYZyZu"><div data-entity-type="code-block" class="sc-AxhCb kEsiDh"><pre class="sc-AxhUy cjr simple-code"><code>[
  {
    "exchange_token": "2885",
    "display_symbol": "RELIANCE-EQ",
    "exchange": "nse_cm",
    "lstup_time": "**********",
    "ltp": "1244.1500",
    "last_traded_quantity": "22",
    "total_buy": "441407",
    "total_sell": "330754",
    "last_volume": "625712",
    "avg_cost": "1239.8500",
    "open_int": "0",
    "change": "-7.0000",
    "per_change": "-0.5600",
    "low_price_range": "1126.0500",
    "high_price_range": "1376.2500",
    "year_high": "3217.6",
    "year_low": "1156",
    "ohlc": {
      "open": "1233.0500",
      "high": "1245.0000",
      "low": "1233.0500",
      "close": "1251.1500"
    },
    "depth": {
      "buy": [
        {
          "price": "1244.0500",
          "quantity": "96",
          "orders": "1"
        },
        {
          "price": "1244.0000",
          "quantity": "1440",
          "orders": "28"
        },
        {
          "price": "1243.9500",
          "quantity": "74",
          "orders": "5"
        },
        {
          "price": "1243.9000",
          "quantity": "604",
          "orders": "2"
        },
        {
          "price": "1243.8500",
          "quantity": "4",
          "orders": "1"
        }
      ],
      "sell": [
        {
          "price": "1244.1500",
          "quantity": "596",
          "orders": "3"
        },
        {
          "price": "1244.2000",
          "quantity": "205",
          "orders": "2"
        },
        {
          "price": "1244.2500",
          "quantity": "159",
          "orders": "1"
        },
        {
          "price": "1244.3000",
          "quantity": "530",
          "orders": "1"
        },
        {
          "price": "1244.4000",
          "quantity": "95",
          "orders": "2"
        }
      ]
    }
  },
  {
    "exchange_token": "532174",
    "display_symbol": "ICICIBANK-A",
    "exchange": "bse_cm",
    "lstup_time": "**********",
    "ltp": "1325.2500",
    "last_traded_quantity": "1",
    "total_buy": "78511",
    "total_sell": "649789",
    "last_volume": "3432",
    "avg_cost": "1321.7900",
    "open_int": "0",
    "change": "-6.2000",
    "per_change": "-0.4700",
    "low_price_range": "1198.3500",
    "high_price_range": "1464.5500",
    "year_high": "1372.5",
    "year_low": "1048.35",
    "ohlc": {
      "open": "1306.5500",
      "high": "1328.0000",
      "low": "1306.5500",
      "close": "1331.4500"
    },
    "depth": {
      "buy": [
        {
          "price": "1324.8500",
          "quantity": "51",
          "orders": "3"
        },
        {
          "price": "1324.8000",
          "quantity": "156",
          "orders": "3"
        },
        {
          "price": "1324.7500",
          "quantity": "47",
          "orders": "3"
        },
        {
          "price": "1324.7000",
          "quantity": "162",
          "orders": "6"
        },
        {
          "price": "1324.6500",
          "quantity": "87",
          "orders": "4"
        }
      ],
      "sell": [
        {
          "price": "1325.8500",
          "quantity": "7",
          "orders": "1"
        },
        {
          "price": "1325.9000",
          "quantity": "33",
          "orders": "1"
        },
        {
          "price": "1326.0000",
          "quantity": "116",
          "orders": "3"
        },
        {
          "price": "1326.0500",
          "quantity": "15",
          "orders": "1"
        },
        {
          "price": "1326.1000",
          "quantity": "30",
          "orders": "2"
        }
      ]
    }
  },
  {
    "exchange_token": "65500",
    "display_symbol": "ABCAPITAL25JUN250PE",
    "exchange": "nse_fo",
    "lstup_time": "Mon Jan 01 1900 05:21:10 GMT+0521 (India Standard Time)",
    "ltp": "0",
    "last_traded_quantity": "0",
    "total_buy": "0",
    "total_sell": "0",
    "last_volume": "0",
    "avg_cost": "",
    "open_int": "0",
    "change": "0",
    "per_change": "0",
    "low_price_range": "37.5",
    "high_price_range": "77.5",
    "year_high": "0",
    "year_low": "0",
    "ohlc": {
      "open": "0",
      "high": "0",
      "low": "0",
      "close": "0"
    },
    "depth": {
      "buy": [
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        },
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        },
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        },
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        },
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        }
      ],
      "sell": [
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        },
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        },
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        },
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        },
        {
          "price": "0",
          "quantity": "0",
          "orders": "0"
        }
      ]
    }
  },
  {
    "exchange_token": "540376",
    "display_symbol": "DMART-A",
    "exchange": "bse_cm",
    "lstup_time": "**********",
    "ltp": "4130.0000",
    "last_traded_quantity": "2",
    "total_buy": "14845",
    "total_sell": "12822",
    "last_volume": "1317",
    "avg_cost": "4120.5200",
    "open_int": "0",
    "change": "13.7000",
    "per_change": "0.3300",
    "low_price_range": "3704.7000",
    "high_price_range": "4527.9000",
    "year_high": "5484",
    "year_low": "3337.1",
    "ohlc": {
      "open": "4116.3500",
      "high": "4136.9000",
      "low": "4079.5000",
      "close": "4116.3000"
    },
    "depth": {
      "buy": [
        {
          "price": "4128.7500",
          "quantity": "15",
          "orders": "4"
        },
        {
          "price": "4128.7000",
          "quantity": "9",
          "orders": "6"
        },
        {
          "price": "4128.6500",
          "quantity": "2",
          "orders": "1"
        },
        {
          "price": "4128.6000",
          "quantity": "2",
          "orders": "1"
        },
        {
          "price": "4128.5500",
          "quantity": "2",
          "orders": "1"
        }
      ],
      "sell": [
        {
          "price": "4135.3500",
          "quantity": "1",
          "orders": "1"
        },
        {
          "price": "4136.0000",
          "quantity": "1",
          "orders": "1"
        },
        {
          "price": "4136.3000",
          "quantity": "2",
          "orders": "1"
        },
        {
          "price": "4136.4500",
          "quantity": "4",
          "orders": "1"
        },
        {
          "price": "4136.5000",
          "quantity": "4",
          "orders": "2"
        }
      ]
    }
  }
]</code></pre></div></div></div></div></div><div class="TabPanel__TabPanelElement-sc-1u751ph-0 epwhuI"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 gCATsr kvd-table " data-testid="aether-flex" data-click=""><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">Date</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">Thu, 03 Apr 2025 03:51:36 GMT</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">Content-Type</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">application/json; charset=utf-8</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">Transfer-Encoding</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">chunked</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">Connection</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">keep-alive</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">x-cache</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">Miss from cloudfront</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">access-control-allow-origin</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">*</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">access-control-allow-methods</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">GET</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">x-content-type-options</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">nosniff</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">Content-Encoding</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">gzip</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">x-amz-cf-pop</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">BOM78-P3</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">access-control-allow-headers</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">authorization,Access-Control-Allow-Origin,Content-Type,SOAPAction,apikey,Internal-Key,consumerKey,testkey,Authorization</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">via</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">1.1 5e293851b64b146aeeed683fba2a5520.cloudfront.net (CloudFront)</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">x-frame-options</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">SAMEORIGIN</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">activityid</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">7e3f924d-4454-4d69-ba67-9c05726544f2</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">access-control-expose-headers</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">strict-transport-security</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">max-age=15552000; includeSubDomains</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">Cache-Control</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">no-store, no-cache, max-age=0</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">etag</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">W/"f5b-RvO7W2mMjMIo7XlkykAEw84S/Ys"</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">content-security-policy</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">default-src 'self';</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">x-amz-cf-id</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">6DScOIshUftf9j4eVD6zwYO0-Ewu5FiBzMXDDzbnifxrO1bP-Pdq7A==</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">Set-Cookie</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">__uzma=cad43c4f-2139-4bab-a66b-cc4a0e29e18b; HttpOnly; path=/; Expires=Thu, 02-Oct-25 03:51:36 GMT ; Max-Age=15724800; SameSite=Lax</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">Set-Cookie</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">__uzmb=**********; HttpOnly; path=/; Expires=Thu, 02-Oct-25 03:51:36 GMT ; Max-Age=15724800; SameSite=Lax</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">Set-Cookie</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">__uzmc=738291030582; HttpOnly; path=/; Expires=Thu, 02-Oct-25 03:51:36 GMT ; Max-Age=15724800; SameSite=Lax</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">Set-Cookie</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">__uzmd=**********; HttpOnly; path=/; Expires=Thu, 02-Oct-25 03:51:36 GMT ; Max-Age=15724800; SameSite=Lax</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">Set-Cookie</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">__uzme=0015; HttpOnly; path=/; Expires=Thu, 02-Oct-25 03:51:36 GMT ; Max-Age=15724800; SameSite=Lax</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">vary</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">Accept-Encoding</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">x-xss-protection</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">1; mode=block</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">cf-cache-status</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">DYNAMIC</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">Server</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">cloudflare</p></div></div></div><div direction="row" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 hQGxbj sc-fzqLLg FlJSP core-request-item-row " data-testid="aether-flex" data-click=""><div style="flex: 1 1 0%;"><span type="" color="content-color-primary" class="Text__TextContainer-sc-1kqigik-0 ecMirJ sc-fznXWL cEvDCP item-key" data-aether-id="aether-text" data-testid="aether-text" data-click="">CF-RAY</span></div><div style="flex: 2 1 0%; overflow: hidden;"><div direction="column" wrap="nowrap" class="Flex__StyledFlex-hyfpms-0 jMvwjn item-value " data-testid="aether-flex" data-click=""><p class="Paragraph__StyledParagraph-sc-1nn5mz5-0 cJReBz" data-aether-id="aether-text-paragraph" type="para" color="content-color-primary" data-testid="aether-text">92a59bc46c4e47f6-BOM</p></div></div></div></div></div></div></div></div></div></div></section></section><section id="53889e4f-531e-4c5c-86ae-ee232745c4a6" style="margin-bottom: 0px;"><section id="53889e4f-531e-4c5c-86ae-ee232745c4a6" class="entitystyles__FolderContainer-sc-kfteh-6 dmqQXo"><div class="entitystyles__FolderDataWrapper-sc-kfteh-5 ebdMLC"><div class="sc-fzomME hTHjyG documentation-core-item-name__text"><h2 class="sc-fzqAbL hLoBGr"><div class="sc-fzqMAW jYXJBZ">Websocket</div></h2></div><div class="sc-fzpkJw gEiHfF"><div class="sc-fzpans hcfROc"><p><span>Kotak Provides two types of websocket</span></p><span>
</span><ul class="sc-fznxsB dhIPUn"><span>
</span><li><p><span>Market Feed - </span><a class="sc-fznMAR fNVHaE markdown-link" href="https://null/" target="_blank" rel="noreferrer noopener nofollow"><span>wss://mlhsm.kotaksecurities.com</span></a></p><span>
</span></li><span>
</span><li><p><span>Real Time Order Updates - refer demo.js file</span></p><span>
</span></li><span>
</span></ul><span>
</span><p><span>Documentation for connecting to these websocket is in the form of SDKs.</span></p><span>
</span><p><span>Refer Below Link for Websocket :</span></p><span>
</span><p><span>JS SDK - </span><a class="sc-fznMAR fNVHaE markdown-link" href="https://drive.google.com/file/d/1EzypWrmB-Uw7PlEw-XSOfmmp2y1mxLfN/view" target="_blank" rel="noreferrer noopener nofollow"><span>https://drive.google.com/file/d/1EzypWrmB-Uw7PlEw-XSOfmmp2y1mxLfN/view</span></a></p><span>
</span><p><span>Python SDK - </span><a class="sc-fznMAR fNVHaE markdown-link" href="https://github.com/Priyanka15802/Neo_sdk_v2" target="_blank" rel="noreferrer noopener nofollow"><span>https://github.com/Priyanka15802/Neo_sdk_v2</span></a></p><span>
</span><p><span>Support Email ID in case of queries - </span><a class="sc-fznMAR fNVHaE markdown-link" href="https://mailto:<EMAIL>/" target="_blank" rel="noreferrer noopener nofollow"><span><EMAIL></span></a></p><span>
</span></div></div></div><div class="entitystyles__CodeblockPlaceholder-sc-kfteh-4 yJneL"></div></section></section></section></main></div></div></div></div></div><div class="ReactModalPortal"></div></body></html>