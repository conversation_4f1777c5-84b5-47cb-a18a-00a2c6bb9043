<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Class Definition Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .log {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Class Definition Test</h1>
        <p>This page tests if all JavaScript classes are loading properly.</p>
        
        <div class="log" id="logOutput">
            <strong>Test Log:</strong><br>
        </div>
    </div>

    <!-- Load scripts in the same order as main app -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/websocket.js"></script>
    <script src="js/trading.js"></script>
    <script src="js/scheduler.js"></script>
    <script src="js/app.js"></script>

    <script>
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logOutput.innerHTML += `<span class="${className}">${timestamp}: ${message}</span><br>`;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Test script loading
        log('Starting class definition tests...', 'info');

        // Test 1: CONFIG object
        try {
            if (typeof CONFIG !== 'undefined') {
                log('✅ CONFIG object loaded successfully', 'success');
                log(`   - API Base URL: ${CONFIG.API.BASE_URL}`, 'info');
                log(`   - OAuth URL: ${CONFIG.API.OAUTH_URL}`, 'info');
            } else {
                log('❌ CONFIG object not found', 'error');
            }
        } catch (error) {
            log(`❌ Error checking CONFIG: ${error.message}`, 'error');
        }

        // Test 2: Logger class
        try {
            if (typeof Logger !== 'undefined') {
                log('✅ Logger class loaded successfully', 'success');
                Logger.log('Test log message from Logger class');
            } else {
                log('❌ Logger class not found', 'error');
            }
        } catch (error) {
            log(`❌ Error checking Logger: ${error.message}`, 'error');
        }

        // Test 3: KotakNeoAPI class
        try {
            if (typeof KotakNeoAPI !== 'undefined') {
                log('✅ KotakNeoAPI class loaded successfully', 'success');
                
                // Try to instantiate it
                const testAPI = new KotakNeoAPI();
                log('✅ KotakNeoAPI instance created successfully', 'success');
                log(`   - Base URL: ${testAPI.baseURL}`, 'info');
                log(`   - OAuth URL: ${testAPI.oauthURL}`, 'info');
                log(`   - Has headers: ${Object.keys(testAPI.headers).length > 0}`, 'info');
            } else {
                log('❌ KotakNeoAPI class not found', 'error');
            }
        } catch (error) {
            log(`❌ Error with KotakNeoAPI: ${error.message}`, 'error');
        }

        // Test 4: TradingManager class
        try {
            if (typeof TradingManager !== 'undefined') {
                log('✅ TradingManager class loaded successfully', 'success');
            } else {
                log('❌ TradingManager class not found', 'error');
            }
        } catch (error) {
            log(`❌ Error checking TradingManager: ${error.message}`, 'error');
        }

        // Test 5: TradingScheduler class
        try {
            if (typeof TradingScheduler !== 'undefined') {
                log('✅ TradingScheduler class loaded successfully', 'success');
            } else {
                log('❌ TradingScheduler class not found', 'error');
            }
        } catch (error) {
            log(`❌ Error checking TradingScheduler: ${error.message}`, 'error');
        }

        // Test 6: TradingBotApp class
        try {
            if (typeof TradingBotApp !== 'undefined') {
                log('✅ TradingBotApp class loaded successfully', 'success');
            } else {
                log('❌ TradingBotApp class not found', 'error');
            }
        } catch (error) {
            log(`❌ Error checking TradingBotApp: ${error.message}`, 'error');
        }

        // Test 7: Try creating all instances (like main app does)
        try {
            log('Testing global instance creation...', 'info');
            
            const testKotakAPI = new KotakNeoAPI();
            log('✅ KotakNeoAPI instance created', 'success');
            
            const testTradingManager = new TradingManager(testKotakAPI);
            log('✅ TradingManager instance created', 'success');
            
            const testTradingScheduler = new TradingScheduler();
            log('✅ TradingScheduler instance created', 'success');
            
            log('🎉 All classes can be instantiated successfully!', 'success');
            
        } catch (error) {
            log(`❌ Error creating instances: ${error.message}`, 'error');
            log(`   Stack: ${error.stack}`, 'error');
        }

        log('Class definition tests completed.', 'info');
    </script>
</body>
</html>
