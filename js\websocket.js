// WebSocket Manager for Real-time Market Data and Order Updates
class WebSocketManager {
    constructor() {
        this.marketDataWS = null;
        this.orderUpdateWS = null;
        this.isMarketDataConnected = false;
        this.isOrderUpdateConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = CONFIG.WEBSOCKET.MAX_RECONNECT_ATTEMPTS;
        this.reconnectInterval = CONFIG.WEBSOCKET.RECONNECT_INTERVAL;
        this.heartbeatInterval = null;
        this.subscribedSymbols = new Set();
        this.messageHandlers = new Map();
        
        // Bind methods to preserve context
        this.onMarketDataOpen = this.onMarketDataOpen.bind(this);
        this.onMarketDataMessage = this.onMarketDataMessage.bind(this);
        this.onMarketDataClose = this.onMarketDataClose.bind(this);
        this.onMarketDataError = this.onMarketDataError.bind(this);
        
        this.onOrderUpdateOpen = this.onOrderUpdateOpen.bind(this);
        this.onOrderUpdateMessage = this.onOrderUpdateMessage.bind(this);
        this.onOrderUpdateClose = this.onOrderUpdateClose.bind(this);
        this.onOrderUpdateError = this.onOrderUpdateError.bind(this);
    }
    
    // Market Data WebSocket Methods
    connectMarketData() {
        try {
            if (this.marketDataWS && this.marketDataWS.readyState === WebSocket.OPEN) {
                Logger.log('Market data WebSocket already connected');
                return;
            }
            
            Logger.log('Connecting to market data WebSocket...');
            this.marketDataWS = new WebSocket(CONFIG.WEBSOCKET.MARKET_FEED_URL);
            
            this.marketDataWS.onopen = this.onMarketDataOpen;
            this.marketDataWS.onmessage = this.onMarketDataMessage;
            this.marketDataWS.onclose = this.onMarketDataClose;
            this.marketDataWS.onerror = this.onMarketDataError;
            
        } catch (error) {
            Logger.error('Failed to connect market data WebSocket', error);
            this.scheduleReconnect('marketData');
        }
    }
    
    onMarketDataOpen(event) {
        Logger.log('Market data WebSocket connected');
        this.isMarketDataConnected = true;
        this.reconnectAttempts = 0;
        
        // Start heartbeat
        this.startHeartbeat();
        
        // Authenticate if needed
        this.authenticateMarketData();
        
        // Re-subscribe to previously subscribed symbols
        this.resubscribeSymbols();
        
        // Notify UI
        this.notifyConnectionStatus('marketData', true);
    }
    
    onMarketDataMessage(event) {
        try {
            const data = JSON.parse(event.data);
            Logger.log('Market data received', data);
            
            // Handle different message types
            switch (data.type) {
                case 'quote':
                    this.handleQuoteUpdate(data);
                    break;
                case 'depth':
                    this.handleDepthUpdate(data);
                    break;
                case 'ack':
                    this.handleAcknowledgment(data);
                    break;
                case 'error':
                    this.handleError(data);
                    break;
                default:
                    Logger.warn('Unknown message type', data);
            }
            
        } catch (error) {
            Logger.error('Error parsing market data message', error);
        }
    }
    
    onMarketDataClose(event) {
        Logger.log('Market data WebSocket disconnected', event);
        this.isMarketDataConnected = false;
        this.stopHeartbeat();
        
        // Notify UI
        this.notifyConnectionStatus('marketData', false);
        
        // Schedule reconnection if not intentional
        if (event.code !== 1000) {
            this.scheduleReconnect('marketData');
        }
    }
    
    onMarketDataError(error) {
        Logger.error('Market data WebSocket error', error);
        this.isMarketDataConnected = false;
        this.scheduleReconnect('marketData');
    }
    
    // Order Update WebSocket Methods
    connectOrderUpdates() {
        try {
            if (this.orderUpdateWS && this.orderUpdateWS.readyState === WebSocket.OPEN) {
                Logger.log('Order update WebSocket already connected');
                return;
            }
            
            Logger.log('Connecting to order update WebSocket...');
            this.orderUpdateWS = new WebSocket(CONFIG.WEBSOCKET.ORDER_UPDATE_URL);
            
            this.orderUpdateWS.onopen = this.onOrderUpdateOpen;
            this.orderUpdateWS.onmessage = this.onOrderUpdateMessage;
            this.orderUpdateWS.onclose = this.onOrderUpdateClose;
            this.orderUpdateWS.onerror = this.onOrderUpdateError;
            
        } catch (error) {
            Logger.error('Failed to connect order update WebSocket', error);
        }
    }
    
    onOrderUpdateOpen(event) {
        Logger.log('Order update WebSocket connected');
        this.isOrderUpdateConnected = true;
        
        // Authenticate for order updates
        this.authenticateOrderUpdates();
    }
    
    onOrderUpdateMessage(event) {
        try {
            const data = JSON.parse(event.data);
            Logger.log('Order update received', data);
            
            // Handle order updates
            this.handleOrderUpdate(data);
            
        } catch (error) {
            Logger.error('Error parsing order update message', error);
        }
    }
    
    onOrderUpdateClose(event) {
        Logger.log('Order update WebSocket disconnected', event);
        this.isOrderUpdateConnected = false;
    }
    
    onOrderUpdateError(error) {
        Logger.error('Order update WebSocket error', error);
        this.isOrderUpdateConnected = false;
    }
    
    // Authentication Methods
    authenticateMarketData() {
        if (!kotakAPI.isAuthenticated) {
            Logger.warn('Cannot authenticate WebSocket - not logged in');
            return;
        }
        
        const authMessage = {
            type: 'auth',
            token: kotakAPI.authToken,
            sid: kotakAPI.sessionId,
            consumerKey: kotakAPI.consumerKey
        };
        
        this.sendMarketDataMessage(authMessage);
    }
    
    authenticateOrderUpdates() {
        if (!kotakAPI.isAuthenticated) {
            Logger.warn('Cannot authenticate order WebSocket - not logged in');
            return;
        }
        
        const authMessage = {
            type: 'auth',
            token: kotakAPI.authToken,
            sid: kotakAPI.sessionId,
            consumerKey: kotakAPI.consumerKey
        };
        
        this.sendOrderUpdateMessage(authMessage);
    }
    
    // Message Sending Methods
    sendMarketDataMessage(message) {
        if (this.marketDataWS && this.marketDataWS.readyState === WebSocket.OPEN) {
            this.marketDataWS.send(JSON.stringify(message));
            Logger.log('Market data message sent', message);
        } else {
            Logger.warn('Cannot send message - market data WebSocket not connected');
        }
    }
    
    sendOrderUpdateMessage(message) {
        if (this.orderUpdateWS && this.orderUpdateWS.readyState === WebSocket.OPEN) {
            this.orderUpdateWS.send(JSON.stringify(message));
            Logger.log('Order update message sent', message);
        } else {
            Logger.warn('Cannot send message - order update WebSocket not connected');
        }
    }
    
    // Symbol Subscription Methods
    subscribeToSymbol(symbol, exchange, mode = 'quote') {
        const subscriptionKey = `${exchange}:${symbol}`;
        
        if (this.subscribedSymbols.has(subscriptionKey)) {
            Logger.log(`Already subscribed to ${subscriptionKey}`);
            return;
        }
        
        const subscribeMessage = {
            type: 'subscribe',
            symbol: symbol,
            exchange: exchange,
            mode: mode
        };
        
        this.sendMarketDataMessage(subscribeMessage);
        this.subscribedSymbols.add(subscriptionKey);
        
        Logger.log(`Subscribed to ${subscriptionKey}`);
    }
    
    unsubscribeFromSymbol(symbol, exchange) {
        const subscriptionKey = `${exchange}:${symbol}`;
        
        if (!this.subscribedSymbols.has(subscriptionKey)) {
            Logger.log(`Not subscribed to ${subscriptionKey}`);
            return;
        }
        
        const unsubscribeMessage = {
            type: 'unsubscribe',
            symbol: symbol,
            exchange: exchange
        };
        
        this.sendMarketDataMessage(unsubscribeMessage);
        this.subscribedSymbols.delete(subscriptionKey);
        
        Logger.log(`Unsubscribed from ${subscriptionKey}`);
    }
    
    resubscribeSymbols() {
        Logger.log('Re-subscribing to symbols...');
        
        for (const subscriptionKey of this.subscribedSymbols) {
            const [exchange, symbol] = subscriptionKey.split(':');
            
            const subscribeMessage = {
                type: 'subscribe',
                symbol: symbol,
                exchange: exchange,
                mode: 'quote'
            };
            
            this.sendMarketDataMessage(subscribeMessage);
        }
    }
    
    // Message Handlers
    handleQuoteUpdate(data) {
        // Notify market data handlers
        this.notifyHandlers('quote', data);
        
        // Update UI
        if (window.marketDataManager) {
            window.marketDataManager.updateQuote(data);
        }
    }
    
    handleDepthUpdate(data) {
        // Notify depth handlers
        this.notifyHandlers('depth', data);
        
        // Update UI
        if (window.marketDataManager) {
            window.marketDataManager.updateDepth(data);
        }
    }
    
    handleOrderUpdate(data) {
        // Notify order handlers
        this.notifyHandlers('order', data);
        
        // Update UI
        if (window.tradingManager) {
            window.tradingManager.handleOrderUpdate(data);
        }
    }
    
    handleAcknowledgment(data) {
        Logger.log('Acknowledgment received', data);
    }
    
    handleError(data) {
        Logger.error('WebSocket error message', data);
        
        // Notify error handlers
        this.notifyHandlers('error', data);
    }
    
    // Event Handler Management
    addMessageHandler(type, handler) {
        if (!this.messageHandlers.has(type)) {
            this.messageHandlers.set(type, []);
        }
        this.messageHandlers.get(type).push(handler);
    }
    
    removeMessageHandler(type, handler) {
        if (this.messageHandlers.has(type)) {
            const handlers = this.messageHandlers.get(type);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }
    
    notifyHandlers(type, data) {
        if (this.messageHandlers.has(type)) {
            const handlers = this.messageHandlers.get(type);
            handlers.forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    Logger.error(`Error in ${type} handler`, error);
                }
            });
        }
    }
    
    // Utility Methods
    startHeartbeat() {
        this.stopHeartbeat();
        
        this.heartbeatInterval = setInterval(() => {
            if (this.isMarketDataConnected) {
                this.sendMarketDataMessage({ type: 'ping' });
            }
        }, CONFIG.WEBSOCKET.HEARTBEAT_INTERVAL);
    }
    
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }
    
    scheduleReconnect(type) {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            Logger.error(`Max reconnection attempts reached for ${type}`);
            return;
        }
        
        this.reconnectAttempts++;
        const delay = this.reconnectInterval * this.reconnectAttempts;
        
        Logger.log(`Scheduling ${type} reconnection in ${delay}ms (attempt ${this.reconnectAttempts})`);
        
        setTimeout(() => {
            if (type === 'marketData') {
                this.connectMarketData();
            } else if (type === 'orderUpdate') {
                this.connectOrderUpdates();
            }
        }, delay);
    }
    
    notifyConnectionStatus(type, connected) {
        // Notify UI about connection status changes
        const event = new CustomEvent('websocketStatusChange', {
            detail: { type, connected }
        });
        document.dispatchEvent(event);
    }
    
    // Cleanup Methods
    disconnect() {
        Logger.log('Disconnecting WebSockets...');
        
        this.stopHeartbeat();
        
        if (this.marketDataWS) {
            this.marketDataWS.close(1000, 'User initiated disconnect');
            this.marketDataWS = null;
        }
        
        if (this.orderUpdateWS) {
            this.orderUpdateWS.close(1000, 'User initiated disconnect');
            this.orderUpdateWS = null;
        }
        
        this.isMarketDataConnected = false;
        this.isOrderUpdateConnected = false;
        this.subscribedSymbols.clear();
    }
    
    // Status Methods
    getConnectionStatus() {
        return {
            marketData: this.isMarketDataConnected,
            orderUpdate: this.isOrderUpdateConnected,
            subscribedSymbols: Array.from(this.subscribedSymbols)
        };
    }
}

// Create global WebSocket manager instance
const wsManager = new WebSocketManager();
