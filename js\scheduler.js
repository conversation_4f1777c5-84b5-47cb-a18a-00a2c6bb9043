// Trading Scheduler - Handles scheduled trades and automated execution
class TradingScheduler {
    constructor() {
        this.scheduledTrades = new Map();
        this.isRunning = false;
        this.checkInterval = CONFIG.SCHEDULER.CHECK_INTERVAL;
        this.intervalId = null;
        this.executionWindow = CONFIG.SCHEDULER.EXECUTION_WINDOW;
        
        // Load scheduled trades from storage
        this.loadScheduledTrades();
        
        // Start the scheduler
        this.start();
    }
    
    // Load scheduled trades from localStorage
    loadScheduledTrades() {
        try {
            const stored = localStorage.getItem(
                CONFIG.STORAGE.STORAGE_PREFIX + CONFIG.STORAGE.KEYS.SCHEDULED_TRADES
            );
            
            if (stored) {
                const trades = JSON.parse(stored);
                
                // Convert stored trades back to Map and Date objects
                for (const trade of trades) {
                    trade.scheduledTime = new Date(trade.scheduledTime);
                    trade.createdAt = new Date(trade.createdAt);
                    
                    // Only load future trades
                    if (trade.scheduledTime > new Date()) {
                        this.scheduledTrades.set(trade.id, trade);
                    }
                }
                
                Logger.log(`Loaded ${this.scheduledTrades.size} scheduled trades`);
            }
        } catch (error) {
            Logger.error('Error loading scheduled trades', error);
        }
    }
    
    // Save scheduled trades to localStorage
    saveScheduledTrades() {
        try {
            const trades = Array.from(this.scheduledTrades.values());
            localStorage.setItem(
                CONFIG.STORAGE.STORAGE_PREFIX + CONFIG.STORAGE.KEYS.SCHEDULED_TRADES,
                JSON.stringify(trades)
            );
        } catch (error) {
            Logger.error('Error saving scheduled trades', error);
        }
    }
    
    // Schedule a new trade
    scheduleTradeExecution(tradeParams) {
        try {
            // Validate trade parameters
            const validation = this.validateScheduledTrade(tradeParams);
            if (!validation.isValid) {
                throw new Error(validation.error);
            }
            
            // Check if we've reached the maximum number of scheduled trades
            if (this.scheduledTrades.size >= CONFIG.SCHEDULER.MAX_SCHEDULED_TRADES) {
                throw new Error('Maximum number of scheduled trades reached');
            }
            
            // Generate unique ID
            const tradeId = this.generateTradeId();
            
            // Create scheduled trade object
            const scheduledTrade = {
                id: tradeId,
                symbol: tradeParams.symbol,
                exchange: tradeParams.exchange,
                transactionType: tradeParams.transactionType,
                quantity: tradeParams.quantity,
                orderType: tradeParams.orderType || 'MKT',
                price: tradeParams.price || 0,
                product: tradeParams.product || CONFIG.TRADING.DEFAULT_PRODUCT,
                stopLoss: tradeParams.stopLoss,
                target: tradeParams.target,
                scheduledTime: new Date(tradeParams.scheduledTime),
                createdAt: new Date(),
                status: 'SCHEDULED',
                attempts: 0,
                maxAttempts: tradeParams.maxAttempts || 3,
                notes: tradeParams.notes || ''
            };
            
            // Add to scheduled trades
            this.scheduledTrades.set(tradeId, scheduledTrade);
            
            // Save to storage
            this.saveScheduledTrades();
            
            Logger.log(`Trade scheduled successfully: ${tradeId}`, scheduledTrade);
            this.notifyTradeScheduled(tradeId, scheduledTrade);
            
            return { success: true, tradeId, scheduledTrade };
            
        } catch (error) {
            Logger.error('Failed to schedule trade', error);
            return { success: false, error: error.message };
        }
    }
    
    // Validate scheduled trade parameters
    validateScheduledTrade(tradeParams) {
        const required = ['symbol', 'exchange', 'transactionType', 'quantity', 'scheduledTime'];
        
        for (const field of required) {
            if (!tradeParams[field]) {
                return { isValid: false, error: `Missing required field: ${field}` };
            }
        }
        
        // Validate scheduled time
        const scheduledTime = new Date(tradeParams.scheduledTime);
        const now = new Date();
        
        if (scheduledTime <= now) {
            return { isValid: false, error: 'Scheduled time must be in the future' };
        }
        
        // Validate quantity
        if (tradeParams.quantity <= 0) {
            return { isValid: false, error: 'Quantity must be greater than 0' };
        }
        
        // Validate price for limit orders
        if (tradeParams.orderType === 'LMT' && (!tradeParams.price || tradeParams.price <= 0)) {
            return { isValid: false, error: 'Price is required for limit orders' };
        }
        
        return { isValid: true };
    }
    
    // Generate unique trade ID
    generateTradeId() {
        return 'ST_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    // Cancel a scheduled trade
    cancelScheduledTrade(tradeId) {
        try {
            const trade = this.scheduledTrades.get(tradeId);
            if (!trade) {
                throw new Error('Scheduled trade not found');
            }
            
            if (trade.status === 'EXECUTED') {
                throw new Error('Cannot cancel executed trade');
            }
            
            // Update status
            trade.status = 'CANCELLED';
            trade.cancelledAt = new Date();
            
            // Remove from scheduled trades
            this.scheduledTrades.delete(tradeId);
            
            // Save to storage
            this.saveScheduledTrades();
            
            Logger.log(`Scheduled trade cancelled: ${tradeId}`);
            this.notifyTradeCancelled(tradeId);
            
            return { success: true };
            
        } catch (error) {
            Logger.error('Failed to cancel scheduled trade', error);
            return { success: false, error: error.message };
        }
    }
    
    // Modify a scheduled trade
    modifyScheduledTrade(tradeId, modifications) {
        try {
            const trade = this.scheduledTrades.get(tradeId);
            if (!trade) {
                throw new Error('Scheduled trade not found');
            }
            
            if (trade.status !== 'SCHEDULED') {
                throw new Error('Can only modify scheduled trades');
            }
            
            // Validate modifications
            if (modifications.scheduledTime) {
                const newTime = new Date(modifications.scheduledTime);
                if (newTime <= new Date()) {
                    throw new Error('New scheduled time must be in the future');
                }
                modifications.scheduledTime = newTime;
            }
            
            // Apply modifications
            Object.assign(trade, modifications);
            trade.modifiedAt = new Date();
            
            // Save to storage
            this.saveScheduledTrades();
            
            Logger.log(`Scheduled trade modified: ${tradeId}`, modifications);
            this.notifyTradeModified(tradeId, modifications);
            
            return { success: true };
            
        } catch (error) {
            Logger.error('Failed to modify scheduled trade', error);
            return { success: false, error: error.message };
        }
    }
    
    // Start the scheduler
    start() {
        if (this.isRunning) {
            Logger.log('Scheduler is already running');
            return;
        }
        
        this.isRunning = true;
        this.intervalId = setInterval(() => {
            this.checkScheduledTrades();
        }, this.checkInterval);
        
        Logger.log('Trading scheduler started');
        this.notifySchedulerStatusChanged(true);
    }
    
    // Stop the scheduler
    stop() {
        if (!this.isRunning) {
            Logger.log('Scheduler is not running');
            return;
        }
        
        this.isRunning = false;
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        
        Logger.log('Trading scheduler stopped');
        this.notifySchedulerStatusChanged(false);
    }
    
    // Check for trades that need to be executed
    checkScheduledTrades() {
        const now = new Date();
        
        for (const [tradeId, trade] of this.scheduledTrades) {
            if (trade.status === 'SCHEDULED') {
                const timeDiff = now - trade.scheduledTime;
                
                // Check if it's time to execute (within execution window)
                if (timeDiff >= 0 && timeDiff <= this.executionWindow) {
                    this.executeTrade(tradeId, trade);
                }
                // Check if trade has expired
                else if (timeDiff > this.executionWindow) {
                    this.expireTrade(tradeId, trade);
                }
            }
        }
        
        // Clean up old trades
        this.cleanupOldTrades();
    }
    
    // Execute a scheduled trade
    async executeTrade(tradeId, trade) {
        try {
            Logger.log(`Executing scheduled trade: ${tradeId}`, trade);
            
            // Update status
            trade.status = 'EXECUTING';
            trade.executionStarted = new Date();
            trade.attempts++;
            
            // Prepare order parameters
            const orderParams = {
                symbol: trade.symbol,
                exchange: trade.exchange,
                transactionType: trade.transactionType,
                quantity: trade.quantity,
                orderType: trade.orderType,
                price: trade.price,
                product: trade.product,
                stopLoss: trade.stopLoss,
                target: trade.target
            };
            
            // Execute the trade
            const result = await tradingManager.placeOrder(orderParams);
            
            if (result.success) {
                // Trade executed successfully
                trade.status = 'EXECUTED';
                trade.executedAt = new Date();
                trade.orderId = result.orderId;
                trade.executionResult = result.data;
                
                // Remove from scheduled trades
                this.scheduledTrades.delete(tradeId);
                
                Logger.log(`Scheduled trade executed successfully: ${tradeId}`);
                this.notifyTradeExecuted(tradeId, trade, result);
                
            } else {
                // Trade execution failed
                if (trade.attempts >= trade.maxAttempts) {
                    // Max attempts reached, mark as failed
                    trade.status = 'FAILED';
                    trade.failedAt = new Date();
                    trade.failureReason = result.error;
                    
                    // Remove from scheduled trades
                    this.scheduledTrades.delete(tradeId);
                    
                    Logger.error(`Scheduled trade failed after ${trade.attempts} attempts: ${tradeId}`);
                    this.notifyTradeFailed(tradeId, trade, result.error);
                    
                } else {
                    // Retry later
                    trade.status = 'SCHEDULED';
                    trade.lastAttemptError = result.error;
                    
                    // Schedule retry in 1 minute
                    trade.scheduledTime = new Date(Date.now() + 60000);
                    
                    Logger.warn(`Scheduled trade failed, will retry: ${tradeId} (attempt ${trade.attempts}/${trade.maxAttempts})`);
                }
            }
            
            // Save to storage
            this.saveScheduledTrades();
            
        } catch (error) {
            Logger.error(`Error executing scheduled trade: ${tradeId}`, error);
            
            // Handle execution error
            trade.status = 'FAILED';
            trade.failedAt = new Date();
            trade.failureReason = error.message;
            
            // Remove from scheduled trades
            this.scheduledTrades.delete(tradeId);
            
            this.notifyTradeFailed(tradeId, trade, error.message);
            this.saveScheduledTrades();
        }
    }
    
    // Mark trade as expired
    expireTrade(tradeId, trade) {
        trade.status = 'EXPIRED';
        trade.expiredAt = new Date();
        
        // Remove from scheduled trades
        this.scheduledTrades.delete(tradeId);
        
        Logger.log(`Scheduled trade expired: ${tradeId}`);
        this.notifyTradeExpired(tradeId, trade);
        
        // Save to storage
        this.saveScheduledTrades();
    }
    
    // Clean up old trades (older than 24 hours)
    cleanupOldTrades() {
        const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
        
        for (const [tradeId, trade] of this.scheduledTrades) {
            if (trade.createdAt < cutoffTime && 
                ['EXECUTED', 'FAILED', 'EXPIRED', 'CANCELLED'].includes(trade.status)) {
                this.scheduledTrades.delete(tradeId);
                Logger.log(`Cleaned up old trade: ${tradeId}`);
            }
        }
    }
    
    // Bulk schedule trades from CSV or array
    bulkScheduleTrades(tradesData) {
        const results = [];
        
        for (const tradeData of tradesData) {
            const result = this.scheduleTradeExecution(tradeData);
            results.push({
                tradeData,
                result
            });
        }
        
        const successful = results.filter(r => r.result.success).length;
        const failed = results.filter(r => !r.result.success).length;
        
        Logger.log(`Bulk schedule completed: ${successful} successful, ${failed} failed`);
        
        return {
            total: results.length,
            successful,
            failed,
            results
        };
    }
    
    // Get scheduled trades with filtering
    getScheduledTrades(filter = {}) {
        let trades = Array.from(this.scheduledTrades.values());
        
        // Apply filters
        if (filter.status) {
            trades = trades.filter(trade => trade.status === filter.status);
        }
        
        if (filter.symbol) {
            trades = trades.filter(trade => 
                trade.symbol.toLowerCase().includes(filter.symbol.toLowerCase())
            );
        }
        
        if (filter.exchange) {
            trades = trades.filter(trade => trade.exchange === filter.exchange);
        }
        
        if (filter.fromDate) {
            const fromDate = new Date(filter.fromDate);
            trades = trades.filter(trade => trade.scheduledTime >= fromDate);
        }
        
        if (filter.toDate) {
            const toDate = new Date(filter.toDate);
            trades = trades.filter(trade => trade.scheduledTime <= toDate);
        }
        
        // Sort by scheduled time
        trades.sort((a, b) => a.scheduledTime - b.scheduledTime);
        
        return trades;
    }
    
    // Get scheduler statistics
    getStatistics() {
        const trades = Array.from(this.scheduledTrades.values());
        
        const stats = {
            total: trades.length,
            scheduled: trades.filter(t => t.status === 'SCHEDULED').length,
            executing: trades.filter(t => t.status === 'EXECUTING').length,
            executed: trades.filter(t => t.status === 'EXECUTED').length,
            failed: trades.filter(t => t.status === 'FAILED').length,
            expired: trades.filter(t => t.status === 'EXPIRED').length,
            cancelled: trades.filter(t => t.status === 'CANCELLED').length,
            isRunning: this.isRunning
        };
        
        return stats;
    }
    
    // Notification Methods
    notifyTradeScheduled(tradeId, trade) {
        const event = new CustomEvent('tradeScheduled', {
            detail: { tradeId, trade }
        });
        document.dispatchEvent(event);
    }
    
    notifyTradeExecuted(tradeId, trade, result) {
        const event = new CustomEvent('tradeExecuted', {
            detail: { tradeId, trade, result }
        });
        document.dispatchEvent(event);
    }
    
    notifyTradeFailed(tradeId, trade, error) {
        const event = new CustomEvent('tradeFailed', {
            detail: { tradeId, trade, error }
        });
        document.dispatchEvent(event);
    }
    
    notifyTradeExpired(tradeId, trade) {
        const event = new CustomEvent('tradeExpired', {
            detail: { tradeId, trade }
        });
        document.dispatchEvent(event);
    }
    
    notifyTradeCancelled(tradeId) {
        const event = new CustomEvent('tradeCancelled', {
            detail: { tradeId }
        });
        document.dispatchEvent(event);
    }
    
    notifyTradeModified(tradeId, modifications) {
        const event = new CustomEvent('tradeModified', {
            detail: { tradeId, modifications }
        });
        document.dispatchEvent(event);
    }
    
    notifySchedulerStatusChanged(isRunning) {
        const event = new CustomEvent('schedulerStatusChanged', {
            detail: { isRunning }
        });
        document.dispatchEvent(event);
    }
}

// Create global scheduler instance
const tradingScheduler = new TradingScheduler();
