<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Bot - Test Suite</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-header {
            background: #007bff;
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
            margin: -20px -20px 20px -20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .test-pass {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .test-fail {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .test-info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #e9ecef;
            border-radius: 8px;
            min-width: 100px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🤖 Kapil Trading Bot - Test Suite</h1>
            <p>Comprehensive testing for all trading bot components</p>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="totalTests">0</div>
                <div class="stat-label">Total Tests</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="passedTests">0</div>
                <div class="stat-label">Passed</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="failedTests">0</div>
                <div class="stat-label">Failed</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="successRate">0%</div>
                <div class="stat-label">Success Rate</div>
            </div>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button onclick="runAllTests()">🚀 Run All Tests</button>
            <button onclick="runConfigTests()">⚙️ Config Tests</button>
            <button onclick="runAPITests()">🔌 API Tests</button>
            <button onclick="runTradingTests()">📈 Trading Tests</button>
            <button onclick="runSchedulerTests()">⏰ Scheduler Tests</button>
            <button onclick="clearResults()">🗑️ Clear Results</button>
        </div>

        <div id="testResults"></div>

        <div class="test-container">
            <h3>📋 Test Log</h3>
            <div class="log-container" id="testLog"></div>
        </div>
    </div>

    <!-- Include the trading bot scripts -->
    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/websocket.js"></script>
    <script src="js/trading.js"></script>
    <script src="js/scheduler.js"></script>

    <script>
        // Test Framework
        class TestFramework {
            constructor() {
                this.tests = [];
                this.results = [];
                this.totalTests = 0;
                this.passedTests = 0;
                this.failedTests = 0;
            }

            addTest(name, testFunction, category = 'General') {
                this.tests.push({
                    name,
                    testFunction,
                    category,
                    status: 'pending'
                });
            }

            async runTest(test) {
                this.log(`Running test: ${test.name}`);
                
                try {
                    const result = await test.testFunction();
                    
                    if (result === true || (result && result.success)) {
                        test.status = 'passed';
                        this.passedTests++;
                        this.log(`✅ PASS: ${test.name}`);
                        this.addResult(test.name, 'PASS', result.message || 'Test passed');
                    } else {
                        test.status = 'failed';
                        this.failedTests++;
                        this.log(`❌ FAIL: ${test.name} - ${result.message || 'Test failed'}`);
                        this.addResult(test.name, 'FAIL', result.message || 'Test failed');
                    }
                } catch (error) {
                    test.status = 'failed';
                    this.failedTests++;
                    this.log(`❌ ERROR: ${test.name} - ${error.message}`);
                    this.addResult(test.name, 'ERROR', error.message);
                }
                
                this.totalTests++;
                this.updateStats();
            }

            async runAllTests() {
                this.clearResults();
                this.log('🚀 Starting comprehensive test suite...');
                
                for (const test of this.tests) {
                    await this.runTest(test);
                    // Small delay between tests
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
                
                this.log(`\n📊 Test Summary: ${this.passedTests}/${this.totalTests} passed`);
            }

            async runTestsByCategory(category) {
                this.clearResults();
                this.log(`🔍 Running ${category} tests...`);
                
                const categoryTests = this.tests.filter(test => test.category === category);
                
                for (const test of categoryTests) {
                    await this.runTest(test);
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
                
                this.log(`\n📊 ${category} Test Summary: ${this.passedTests}/${this.totalTests} passed`);
            }

            addResult(testName, status, message) {
                const resultsContainer = document.getElementById('testResults');
                const resultDiv = document.createElement('div');
                resultDiv.className = `test-result test-${status.toLowerCase() === 'pass' ? 'pass' : 'fail'}`;
                resultDiv.innerHTML = `
                    <strong>${status}: ${testName}</strong><br>
                    <small>${message}</small>
                `;
                resultsContainer.appendChild(resultDiv);
            }

            log(message) {
                const logContainer = document.getElementById('testLog');
                const timestamp = new Date().toLocaleTimeString();
                logContainer.innerHTML += `[${timestamp}] ${message}\n`;
                logContainer.scrollTop = logContainer.scrollHeight;
                console.log(message);
            }

            updateStats() {
                document.getElementById('totalTests').textContent = this.totalTests;
                document.getElementById('passedTests').textContent = this.passedTests;
                document.getElementById('failedTests').textContent = this.failedTests;
                
                const successRate = this.totalTests > 0 ? 
                    Math.round((this.passedTests / this.totalTests) * 100) : 0;
                document.getElementById('successRate').textContent = successRate + '%';
            }

            clearResults() {
                this.results = [];
                this.totalTests = 0;
                this.passedTests = 0;
                this.failedTests = 0;
                document.getElementById('testResults').innerHTML = '';
                document.getElementById('testLog').innerHTML = '';
                this.updateStats();
            }
        }

        // Initialize test framework
        const testFramework = new TestFramework();

        // Configuration Tests
        testFramework.addTest('Config Object Exists', () => {
            return typeof CONFIG !== 'undefined';
        }, 'Config');

        testFramework.addTest('Config Validation', () => {
            try {
                ConfigUtils.validate();
                return { success: true, message: 'Configuration is valid' };
            } catch (error) {
                return { success: false, message: error.message };
            }
        }, 'Config');

        testFramework.addTest('Config Get/Set Functions', () => {
            const testValue = 'test_value_123';
            ConfigUtils.set('TEST.VALUE', testValue);
            const retrievedValue = ConfigUtils.get('TEST.VALUE');
            return retrievedValue === testValue;
        }, 'Config');

        // API Tests
        testFramework.addTest('API Instance Creation', () => {
            return typeof kotakAPI !== 'undefined' && kotakAPI instanceof KotakNeoAPI;
        }, 'API');

        testFramework.addTest('API Methods Exist', () => {
            const requiredMethods = ['login', 'logout', 'placeOrder', 'getOrderBook', 'getPositions'];
            for (const method of requiredMethods) {
                if (typeof kotakAPI[method] !== 'function') {
                    return { success: false, message: `Missing method: ${method}` };
                }
            }
            return { success: true, message: 'All required API methods exist' };
        }, 'API');

        testFramework.addTest('API Headers Configuration', () => {
            return kotakAPI.headers && typeof kotakAPI.headers === 'object';
        }, 'API');

        // WebSocket Tests
        testFramework.addTest('WebSocket Manager Creation', () => {
            return typeof wsManager !== 'undefined' && wsManager instanceof WebSocketManager;
        }, 'WebSocket');

        testFramework.addTest('WebSocket Methods Exist', () => {
            const requiredMethods = ['connectMarketData', 'subscribeToSymbol', 'unsubscribeFromSymbol'];
            for (const method of requiredMethods) {
                if (typeof wsManager[method] !== 'function') {
                    return { success: false, message: `Missing method: ${method}` };
                }
            }
            return { success: true, message: 'All required WebSocket methods exist' };
        }, 'WebSocket');

        // Trading Tests
        testFramework.addTest('Trading Manager Creation', () => {
            return typeof tradingManager !== 'undefined' && tradingManager instanceof TradingManager;
        }, 'Trading');

        testFramework.addTest('Order Validation', () => {
            const validOrder = {
                symbol: 'RELIANCE',
                exchange: 'NSE',
                transactionType: 'B',
                quantity: 100
            };
            
            const validation = tradingManager.validateOrder(validOrder);
            return validation.isValid;
        }, 'Trading');

        testFramework.addTest('Invalid Order Rejection', () => {
            const invalidOrder = {
                symbol: 'RELIANCE',
                // Missing required fields
            };
            
            const validation = tradingManager.validateOrder(invalidOrder);
            return !validation.isValid;
        }, 'Trading');

        testFramework.addTest('Risk Manager Creation', () => {
            return tradingManager.riskManager instanceof RiskManager;
        }, 'Trading');

        // Scheduler Tests
        testFramework.addTest('Scheduler Creation', () => {
            return typeof tradingScheduler !== 'undefined' && tradingScheduler instanceof TradingScheduler;
        }, 'Scheduler');

        testFramework.addTest('Scheduler Running Status', () => {
            return tradingScheduler.isRunning === true;
        }, 'Scheduler');

        testFramework.addTest('Schedule Trade Validation', () => {
            const validTrade = {
                symbol: 'RELIANCE',
                exchange: 'NSE',
                transactionType: 'B',
                quantity: 100,
                scheduledTime: new Date(Date.now() + 60000) // 1 minute from now
            };
            
            const validation = tradingScheduler.validateScheduledTrade(validTrade);
            return validation.isValid;
        }, 'Scheduler');

        testFramework.addTest('Invalid Schedule Rejection', () => {
            const invalidTrade = {
                symbol: 'RELIANCE',
                exchange: 'NSE',
                transactionType: 'B',
                quantity: 100,
                scheduledTime: new Date(Date.now() - 60000) // 1 minute ago (past)
            };
            
            const validation = tradingScheduler.validateScheduledTrade(invalidTrade);
            return !validation.isValid;
        }, 'Scheduler');

        // Logger Tests
        testFramework.addTest('Logger Functions', () => {
            try {
                Logger.log('Test log message');
                Logger.warn('Test warning message');
                Logger.error('Test error message');
                return { success: true, message: 'Logger functions work correctly' };
            } catch (error) {
                return { success: false, message: error.message };
            }
        }, 'General');

        // Local Storage Tests
        testFramework.addTest('Local Storage Access', () => {
            try {
                const testKey = 'test_storage_key';
                const testValue = 'test_storage_value';
                
                localStorage.setItem(testKey, testValue);
                const retrieved = localStorage.getItem(testKey);
                localStorage.removeItem(testKey);
                
                return retrieved === testValue;
            } catch (error) {
                return { success: false, message: 'Local storage not available' };
            }
        }, 'General');

        // Browser Compatibility Tests
        testFramework.addTest('WebSocket Support', () => {
            return typeof WebSocket !== 'undefined';
        }, 'Browser');

        testFramework.addTest('Fetch API Support', () => {
            return typeof fetch !== 'undefined';
        }, 'Browser');

        testFramework.addTest('JSON Support', () => {
            return typeof JSON !== 'undefined' && typeof JSON.parse === 'function';
        }, 'Browser');

        testFramework.addTest('Date Object Support', () => {
            try {
                const date = new Date();
                return date instanceof Date;
            } catch (error) {
                return false;
            }
        }, 'Browser');

        // UI Tests
        testFramework.addTest('Required DOM Elements', () => {
            const requiredElements = [
                'connectionStatus',
                'connectBtn',
                'loginBtn',
                'subscribeBtn',
                'placeOrderBtn',
                'scheduleTradeBtn'
            ];
            
            for (const elementId of requiredElements) {
                if (!document.getElementById(elementId)) {
                    return { success: false, message: `Missing element: ${elementId}` };
                }
            }
            
            return { success: true, message: 'All required DOM elements found' };
        }, 'UI');

        // Test Runner Functions
        function runAllTests() {
            testFramework.runAllTests();
        }

        function runConfigTests() {
            testFramework.runTestsByCategory('Config');
        }

        function runAPITests() {
            testFramework.runTestsByCategory('API');
        }

        function runTradingTests() {
            testFramework.runTestsByCategory('Trading');
        }

        function runSchedulerTests() {
            testFramework.runTestsByCategory('Scheduler');
        }

        function clearResults() {
            testFramework.clearResults();
        }

        // Auto-run basic tests on page load
        document.addEventListener('DOMContentLoaded', () => {
            testFramework.log('🔧 Test suite loaded successfully');
            testFramework.log('Click "Run All Tests" to start comprehensive testing');
            
            // Run a quick smoke test
            setTimeout(() => {
                testFramework.log('🔍 Running smoke test...');
                testFramework.runTestsByCategory('Config');
            }, 1000);
        });
    </script>
</body>
</html>
